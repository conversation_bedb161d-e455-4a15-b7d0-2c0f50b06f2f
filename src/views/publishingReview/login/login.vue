<template>
  <div class="main">
    <img
      :class="!isAuthor?'title':'title_author'"
      :src="!isAuthor?require('../../../assets/publishingReview/title.png'):require('../../../assets/publishingReview/author.png')"
      alt=""
    />
    <img v-if="!isAuthor" class="to_author" src="../../../assets/publishingReview/toAuthor.png" @click="toAuthor" alt=""/>
    <img v-if="!isAuthor" class="cover" :src="imgSrc" alt="" />
    <div class="login_box">
      <div class="change">
        <div :class="loginType=='PASSWORD'?'selected':''" @click="loginType='PASSWORD'">密码登录</div>
        <div :class="loginType=='SMS_CODE'?'selected':''" @click="loginType='SMS_CODE'">验证码登录</div>
      </div>
      <div class="form">
        <el-form ref="form" class="el_from" label-width="4vw">
          <el-form-item label="手机号">
            <el-input v-model="form.mobileOrEmail" type="text" />
          </el-form-item>
          <el-form-item v-if="loginType=='SMS_CODE'" label="验证码" style="position: relative">
            <el-input v-model="form.code" />
            <el-button
              type="info"
              :disabled="smsDisabled"
              class="sms-btn"
              @click="getCode"
            >
              {{
                smsDisabled
                  ? countdown > 0
                    ? countdown + 's后重新获取'
                    : '发送验证码'
                  : '发送验证码'
              }}
            </el-button>
          </el-form-item>
          <el-form-item v-if="loginType=='PASSWORD'" label="密码" >
            <el-input v-model="form.password" type="password" show-password	/>
          </el-form-item>
          <el-form-item label="">
            <el-button
              type="primary"
              class="button"
              @click="login"
            >登录</el-button>
          </el-form-item>
        </el-form>
      </div>
      <img src="../../../assets/publishingReview/login_right.png" alt="" />
    </div>
    <!-- 底部备案号 -->
    <div class="icp">
      <a
        href="https://beian.miit.gov.cn"
        target="_blank"
      >ICP备案号：{{ record }}</a>
    </div>
  </div>
</template>
<script>
import { getPublisher } from '@/api/publishing'
import { validMobile } from '@/utils/validate'
import { verifyCodeForWeb } from '@/api/user-api'
export default {
  data() {
    return {
      imgSrc: '',
      isAuthor: false,
      form: {
        mobileOrEmail: '',
        code: '',
        loginType: '',
        password: ''
      },
      loginType: 'PASSWORD',
      smsDisabled: false,
      countdown: 0
    }
  },
  computed: {
    record () {
      // const host = window.location.host
      // if (host.indexOf('binguoketang.com') > -1) {
      return '蜀ICP备2021023754号-4'
      // } else {
      //   return ''
      // }
    }
  },
  mounted() {
    if (window.location.href.indexOf('publisher') > -1 && this.$route.params.publisheId) {
      localStorage.setItem('publisheId', this.$route.params.publisheId)
      getPublisher({ pubulisherId: this.$route.params.publisheId }).then(
        (res) => {
          this.imgSrc = res.data.coverUrl
        }
      )
    }
    if (window.location.href.indexOf('author') > -1) {
      this.isAuthor = true
    }
    document.title = this.$route.meta.title
  },
  methods: {
    toAuthor() {
      this.$router.push({ path: '/author/login' })
      this.isAuthor = true
    },
    login() {
      const _this = this
      if (window.location.href.indexOf('publisher') > -1) {
        this.form.loginType = this.loginType
        this.$store.dispatch('user/PublishLogin', this.form).then(() => {
          _this.$router.push({
            path: '/publisher/home',
            query: { publisheId: this.$route.params.publisheId }
          })
          this.$nextTick(() => {
            this.$store.dispatch('user/GetInfo')
          })
        })
      } else if (window.location.href.indexOf('expert') > -1) {
        this.form.loginType = this.loginType
        this.$store.dispatch('user/ExpertLogin', this.form).then(() => {
          _this.$router.push({ path: '/expert/home' })
          this.$nextTick(() => {
            this.$store.dispatch('user/GetInfo')
          })
        })
      } else if (window.location.href.indexOf('author') > -1) {
        this.form.loginType = this.loginType
        this.$store.dispatch('user/AuthorLogin', this.form).then(() => {
          _this.$router.push({ path: '/author/home' })
          this.$nextTick(() => {
            this.$store.dispatch('user/GetInfo')
          })
        })
      }
    },
    getCode() {
      if (this.smsDisabled) return
      const mobile = this.form.mobileOrEmail
      console.log(mobile)
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this._verifyCodeForWeb()
      // this.nocaptchaVisible = true
    },
    _verifyCodeForWeb() {
      verifyCodeForWeb({ mobile: this.form.mobileOrEmail }).then((res) => {
        this.countdown = 60
        this.smsDisabled = true
        setTimeout(this.tick, 1000)
      })
    },
    tick() {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input {
  font-size: var(--font-size-M);
  padding: 4px;
}
::v-deep .el-form-item__label{
  font-size: 10px;
  padding: 4px;
  white-space: nowrap;
}
::v-deep .el-form-item {
  margin-bottom: 10px;
}

::v-deep .el-input__inner {
  height: 30px;
  padding: 5px;
}

::v-deep .el-button {
  padding: 10px;
  font-size: var(--font-size-M);
}
.icp{
  position: absolute;
  bottom: 5vh;
  left: 480px;
  font-size: var(--font-size-M);
}
.main {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
.to_author{
  width: 80px;
  position: absolute;
  right: 20px;
  top:20px;
  cursor: pointer;
}
  .cover {
    position: absolute;
    width: 300px;
    left: 20px;
    top: 20px;
    z-index: 0;
  }
  .title {
    width: 200px;
    position: absolute;
    left: 450px;
    top: 12vh;
    z-index: 0;
  }
.title_author{
    width: 300px;
    position: absolute;
    left: 370px;
    top: 12vh;
    z-index: 0;
}
  .login_box {
    width: 692px;
    height: 306px;
    margin: 30vh auto;
    background: #fff;
    border-radius: 10px;
    display: flex;
    position: relative;
    // z-index: 1;
    justify-content: space-between;
   .change{
    width: 140px;
    height: 30px;
    position: absolute;
    left: 110px;
    top:10px;
    display: flex;
    font-size: var(--font-size-L);
    color: #828282;
    justify-content: space-between;
    div{
      width: 56px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      white-space: nowrap;
      cursor: pointer;
    }
    .selected{
      color: #2F80ED;
      border-bottom: 2px solid #2F80ED; ;
    }
   }
    .el_from {
      width: 300px;
    }

    .form {
      margin-top: 50px;
      margin-left: 20px;

      .button {
        width: 98%;
        margin-top: 20px;
        margin-left: 5px;
      }

      .sms-btn {
        position: absolute;
        top: 10px;
        right: 5px;
        margin: 3px;
        padding: 0;
        width: 55px;
        font-size: 8px;
        height: 20px;
        line-height: 20px;
        border-radius: 6px;
        font-weight: 500;
        background: #1f66ff;
        color: #ffffff;
        border: none;
        box-sizing: border-box !important;
        // box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.16);

        // &.is-disabled {
        //   border: #ffffff;
        //   background: #a1a1a1;
        //   color: #ffffff;
        // }

        // &.is-disabled:hover {
        //   border: #ffffff;
        //   background: #a1a1a1;
        //   color: #ffffff;
        // }
      }

      .sms-btn:hover {
        background: #1f66ff;
        box-shadow: 1px 1px 1px #fff;
      }
    }
  }
}
</style>
