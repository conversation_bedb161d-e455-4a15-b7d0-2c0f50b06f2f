<template>
  <div class="editor-dig">
    <div class="dig-head">
      <div>
        <div class="pointer flex items-center" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          返回
        </div>
      </div>
      <div>{{ bookTitle }}</div>
      <div class="flex">
        <el-popover
          placement="bottom-end"
          width="400"
          trigger="click"
          popper-class="review-detail-popover"
          @show="_getDigitalBookReviewUserByBook"
        >
          <div class="review-detail">
            <div v-for="(item, index) in reviewList" :key="index" class="review-item">
              <div class="reviewer">
                <div class="left-content">
                  <span class="name">{{ item.user&&item.user.displayName }}</span>
                  <span>({{ item.role ? fomatterAuthor(item.role) : '审核' }})</span>
                </div>
                <div class="right-content">
                  <div class="status-wrapper">
                    <span class="dot" :class="getStatusClass(item.reviewStatus)"></span>
                    <span class="status">{{ formatStatus(item.reviewStatus) }}</span>
                  </div>
                  <el-popover
                    v-if="item.reviewStatus !== 'UNDER_REVIEW'"
                    placement="top"
                    width="300"
                    trigger="click"
                  >
                    <div class="review-opinion">
                      <div class="opinion-title">审核意见</div>
                      <div class="opinion-content">{{ item.reviewOpinion || '暂无审核意见' }}</div>
                    </div>
                    <el-button
                      slot="reference"
                      type="text"
                      size="mini"
                      class="view-btn"
                    >
                      查看
                    </el-button>
                  </el-popover>
                </div>
              </div>
            </div>
          </div>
          <el-button slot="reference" type="text">审核详情</el-button>
        </el-popover>
        <el-button type="danger" @click="Reject">返修</el-button>
        <el-button type="success" @click="subMitBook">{{ submitType }}</el-button>
        <el-button type="primary" @click="showExportDialog">导出</el-button>
      </div>
    </div>
    <div class="dig-box">
      <div class="dig-left">
        <div class="chapter-title">
          <div class="icon1">
            <img src="../../../assets/digitalbooks/chapter.svg" />
            目录
          </div>
          <div class="flex items-center">
            <!-- <div class="add-btn" @click="treeAppend(null, null)">
              +新建章节/任务
            </div> -->
            <!-- <i class="el-icon-s-fold pointer"></i> -->
            <!-- <i class="el-icon-s-unfold pointer"></i> -->
          </div>
        </div>
        <div class="chapter-body">
          <el-tree
            ref="treeSelect"
            :data="bookTree"
            :props="treeProps"
            node-key="id"
            :current-node-key="currentKey"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
            @node-drag-start="handleDragStart"
            @node-drop="handleDrop"
            @node-click="handleNodeClick"
          >
            <div slot-scope="{ data }" class="tree-body">
              <div class="chapter-name">
                <div :title="data.title" class="w article-singer-container">
                  {{ data.title }}
                </div>
              </div>
              <div v-if="data.bugQuantity !== 0" class="chapter-option">
                {{ data.bugQuantity }}
              </div>
            </div>
          </el-tree>
        </div>
      </div>
      <div v-loading="loading" class="dig-center">
        <div v-show="otherUse" class="other-use">
          <div style="background-color: white;min-width: 1vw;position: absolute;left: 50%;top: 50%">已被其它用户占用</div>
        </div>
        <!-- <div class="toolbar">
          <Toolbar
            v-if="!loading"
            :editor="editor"
            :default-config="toolbarConfig"
            :mode="mode"
          />
        </div> -->
        <div class="eidtor">
          <tinymce-editor
            id="tinymce"
            v-model="html"
            :init="init"
            @onFocus="onFocus"
            @onChange="onChange"
            @onBlur="onBlur"
          />
        </div>
      </div>
      <div class="right_content">
        <el-tabs v-model="activeName" stretch>
          <el-tab-pane label="审核意见" name="first">
            <div v-if="bugList.length === 0" class="w" style="height: 90%">
              <Empty :msg="'暂无数据'" style="transform: scale(0.6);" />
            </div>
            <div v-else class="bug_list_header">
              <div
                :class="{ active: currentFilterType === 'ALL' }"
                @click="currentFilterType = 'ALL'"
              >全部</div>
              <div
                :class="{ active: currentFilterType === 'OPEN' }"
                @click="currentFilterType = 'OPEN'"
              >未解决</div>
              <!-- <div
                :class="{ active: currentFilterType === 'CLOSED' }"
                @click="currentFilterType = 'CLOSED'"
              >已解决</div> -->
            </div>
            <div v-for="item in filteredBugList" :key="item.id" class="bug_item">
              <div style='width: 100%;height: 100%;cursor: pointer' @click="toTop(item)">
                <div class="bug_item_header">
                  <div v-if="publishStatus === 'PUBLISH'" class="bug_item_header_author_container">
                    <div class="bug_item_header_author" :class="item.userRole === 'REVIEW_1' ? 'review_1' : item.userRole === 'REVIEW_2' ? 'review_2' : item.userRole === 'REVIEW_3' ? 'review_3' : item.userRole === 'PROOFREAD' ? 'proofread' : 'review'">{{ fomatterAuthor(item.userRole) }}</div>
                    <p :class="item.userRole === 'REVIEW_1' ? 'review_1_t' : item.userRole === 'REVIEW_2' ? 'review_2_t' : item.userRole === 'REVIEW_3' ? 'review_3_t' : item.userRole === 'PROOFREAD' ? 'proofread_t' : 'review_t'">{{ item.user.displayName }}</p>
                  </div>
                  <el-button v-if="item.bugStatus==='FIXED'" type="text" style="margin-left: 10px; " @click="closeBug(item)">复核无误</el-button>
                  <span style="margin-left: 10px;">{{ item.createdAt }} </span>
                  <el-button v-if="item.bugStatus!=='FIXED'&&item.user.id===currentUserId" style="margin-left: 10px;" type="text" @click.stop="updateBug(item)">修改</el-button>
                  <el-button v-if="item.bugStatus!=='FIXED'&&item.user.id===currentUserId" type="text" style="margin-left: 10px; color: red;" @click.stop="deleteBug(item)">删除</el-button>
                  <el-tag class="tag" :type="formatType(item.bugStatus)">{{ formatTypeText(item.bugStatus) }}</el-tag>
                </div>
                <div class="bug_text">引用：<div v-html="item.text"></div>
                </div>
                <div class="bug_content">{{ item.bugDescription }}</div>
              </div>
              <el-collapse accordion>
                <el-collapse-item>
                  <template slot="title">
                    留言 <p class="add" @click="openMessage(item, false)">共有{{ item.commentQuantity }}条留言</p>
                  </template>
                  <div class="message_content">
                    <div v-for="(item1, index) in item.commentList" :key="index" class="message_item">
                      <div class="header">
                        <el-tag class="message_tag" size="mini" :type="item1.userRole=='作者'?'warning':''">{{ fomatterAuthor(item1.userRole) }}</el-tag>
                        <span style="margin-left: 5px;">{{ item1.createdAt }}</span>
                      </div>
                      <div class="message_content">{{ item1.content }}</div>
                    </div>
                  </div>
                  <div class="submit_message">
                    <el-input v-model="message" class="message_input" placeholder="请输入留言" />
                    <el-button class="message_button" type="primary" @click.stop="submit_message(item)">提交</el-button>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-tab-pane>
          <el-tab-pane label="版本记录" name="second">
            <div v-if="BookReviewList.length === 0" class="w" style="height: 90%">
              <Empty :msg="'暂无数据'" style="transform: scale(0.6);" />
            </div>
            <div v-for="(item,index) in BookReviewList" :key="index" class="review_item">
              <p class="title">{{ item.edition }}</p>
              <p class="time">{{ timestampToTime(item.reviewTimePublish) }}</p>
              <el-button type="primary" plain class="open" @click="openReviewRead(item)">打开</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div v-if="preShow" class="pre">
      <Read :pre-mode="true" :node="preNode" :pre-publish-mode="true" :pre-edition-id="preEditionId" @close="preShow = false" />
    </div>
    <tipsPop ref="tips" :position="tipsPositon" :info="tipsInfo" />
    <imgGroupPop ref="imgs" :info="imgListInfo" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <publishPop ref="publish" :info="publishInfo" />
    <doTest ref="doTest" :test-id="testId" :ids="ids" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <reviewDialog ref="reviewDialog" />
    <ExportDialog
      ref="exportDialog"
      :visible.sync="exportDialogVisible"
      :book-id="bookInfo.id"
      :digital-book-review-id="digitalBookReviewId"
      @export="handleExport"
      @close="handleExportDialogClose"
    />
  </div>
</template>

<script>
import tinymce from 'tinymce/tinymce'
import Empty from '@/components/classPro/Empty/index.vue'
import TinymceEditor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver/theme'
import { getBook, deleteBookCatalogue, saveContent, getContentEditor, getBookCatalogueByVersion, dragCatalogue, takeBook } from '@/api/digital-api.js'
import { getExpertToken, getPublishToken } from '@/utils/auth'
import { findParentByClass } from '@/utils/dom'
import { Notification } from 'element-ui'
import { throttle } from '@/utils/index'
import { saveAs } from 'file-saver'
import defaultConfig from './utils/config'
import { addTag } from './utils/addTag'
import { getSqlPlatformToken } from '@/api/training-api'
import { getDigitalBugList, changeDigitalBugStatus, changeDigitalBookReviewStatus, updateDigitalBug, submitDigitalBugComment, getDigitalBugCommentList, getDigitalBookReviewListByBook, getDigitalBookReviewUserByBook } from '@/api/publishing'
import { EventBus } from './utils/event'
import { addTagModal, closeTag, setData } from './components/addTag'
import tipsPop from './components/tipsPop.vue'
import imgGroupPop from './components/imgGroupPop.vue'
import videoCardPop from './components/videoPop.vue'
import publishPop from './components/publishPop.vue'
import { addTagToast } from './utils/tagToast'
import officeView from '../../digitalbooks/editor/components/officeView.vue'
import Read from '@/views/digitalbooks/read/index.vue'
import doTest from '../../digitalbooks/editor/components/doTest.vue'
import { mapGetters } from 'vuex'
import reviewDialog from './components/reviewDialog.vue'
import ExportDialog from './components/ExportDialog.vue'
tinymce.PluginManager.add('addTagToast', addTagToast)
tinymce.PluginManager.add('addTag', addTag)
export default {
  components: { TinymceEditor, tipsPop, imgGroupPop, videoCardPop, Empty, publishPop, Read, doTest, officeView, reviewDialog, ExportDialog },
  data() {
    return {
      testId: '0',
      ids: '',
      tinymceId: 'tinymce-' + Date.now() + Math.floor(Math.random() * 1000),
      init: Object.assign(defaultConfig, {
      }),
      submitType: '审核通过',
      activeName: 'first',
      message: '',
      preEditionId: '0',
      showTag: false,
      publishInfo: null,
      bugList: [],
      reviewList: [],
      tipsPositon: {
        top: 0,
        left: 0
      },
      tagPosition: {
        top: 0,
        left: 0
      },
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      },
      tipsInfo: {
        keyword: '',
        content: ''
      },
      bookInfo: null,
      preShow: false,
      editor: null,
      chapterShow: true,
      loading: false,
      html: '<p><br></p>',
      newhtml: '',
      mode: 'default', // or 'simple'
      bookId: 0,
      treeData: null,
      bookTitle: '',
      bookTree: [],
      BookReviewList: [],
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      currChapter: null,
      editChapterShow: false,
      selectTreeId: 0,
      preNode: null,
      token: '',
      saveTimer: null,
      autoTips: false,
      otherUse: false,
      keepHeartTime: null,
      contentChanged: false,
      exportDialogVisible: false,
      imgListInfo: null,
      openFlag: false,
      officeUrl: '',
      isLoading: false,
      currentKey: 0,
      publishStatus: '',
      currentUserId: '',
      currentFilterType: 'ALL'
    }
  },
  computed: {
    ...mapGetters(['id']),
    digitalBookReviewId() {
      return this.$route.query.id
    },
    filteredBugList() {
      if (this.currentFilterType === 'ALL') {
        return this.bugList
      } else if (this.currentFilterType === 'OPEN') {
        return this.bugList.filter(item => item.bugStatus === 'OPEN')
      } else {
        return this.bugList.filter(item => item.bugStatus === 'CLOSE' || item.bugStatus === 'FIXED')
      }
    }
  },
  mounted() {
    document.title = this.$route.meta.title
    this.token = window.location.href.indexOf('publisher') > -1 ? getPublishToken() : getExpertToken()
    this.publishStatus = window.location.href.indexOf('publisher') > -1 ? 'PUBLISH' : 'EXPERT'
    // this.token = `Bearer ${this.$route.query && this.$route.query.token}`
    this.bookId = this.$route.query && this.$route.query.bookId
    // this._takeBook()
    this._getBook()
    this._getBookCatalogue()
    this._getDigitalBookReviewListByBook()
    this._getDigitalBookReviewUserByBook()
    // window.addEventListener('beforeunload', e => this.beforeunloadFn(e))
    this.setType()
    tinymce.init({}).then(() => {
      // tinymce.activeEditor.setMode('readonly')
      setTimeout(() => {
        tinymce.activeEditor.getBody().setAttribute('contenteditable', false)
        setTimeout(() => {
          this.initFun()
        }, 1000)
      }, 1000)
    })
    EventBus.$on('reLoad', (msg) => {
      this.getBugList()
      this._getBookCatalogue()
    })
    this.currentUserId = this.getCurrentUserId()
  },
  async beforeDestroy() {
    if (this.keepHeartTime) {
      clearInterval(this.keepHeartTime)
      this.keepHeartTime = null
    }
  },
  methods: {
    showExportDialog() {
      this.exportDialogVisible = true
    },
    handleExport(exportData) {
      const { content, format, totalChapters, includeReviews, errors, bookId } = exportData

      console.log(`开始导出${format === 'pdf' ? 'PDF' : 'Word'}文档...`, {
        bookId,
        totalChapters,
        includeReviews,
        contentLength: content.length,
        format
      })

      if (errors && errors.length > 0) {
        console.warn(`导出过程中遇到 ${errors.length} 个错误:`, errors)
      }

      if (format === 'pdf') {
        this.exportToPDF(content, bookId, totalChapters)
      } else {
        this.exportToWord(content, bookId, totalChapters)
      }
    },

    exportToWord(content, bookId, totalChapters) {
      try {
        const fullHtmlContent = this.createWordCompatibleHtml(content)

        const blob = new Blob([fullHtmlContent], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8'
        })

        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        const fileName = `数字教材导出_书籍${bookId}_${totalChapters}章节_${new Date().toISOString().slice(0, 10)}.doc`
        link.download = fileName

        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
      } catch (error) {
        console.error('导出Word文档失败:', error)
      }
    },

    createWordCompatibleHtml(content) {
      return `
        <!DOCTYPE html>
        <html xmlns:o="urn:schemas-microsoft-com:office:office"
              xmlns:w="urn:schemas-microsoft-com:office:word"
              xmlns="http://www.w3.org/TR/REC-html40">
        <head>
          <meta charset="utf-8">
          <title>数字教材导出文档</title>
          <!--[if gte mso 9]>
          <xml>
            <w:WordDocument>
              <w:View>Print</w:View>
              <w:Zoom>90</w:Zoom>
              <w:DoNotPromptForConvert/>
              <w:DoNotShowRevisions/>
              <w:DoNotPrintRevisions/>
              <w:DoNotShowMarkup/>
              <w:DoNotShowComments/>
              <w:DoNotShowInsertionsAndDeletions/>
              <w:DoNotShowPropertyChanges/>
            </w:WordDocument>
          </xml>
          <![endif]-->
          <style>
            body {
              font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
              font-size: 12pt;
              line-height: 1.6;
              margin: 1in;
              color: #000;
            }
            h1, h2, h3, h4, h5, h6 {
              color: #333;
              margin-top: 24pt;
              margin-bottom: 12pt;
            }
            h1 { font-size: 18pt; }
            h2 { font-size: 16pt; }
            h3 { font-size: 14pt; }
            h4 { font-size: 13pt; }
            p { margin-bottom: 12pt; }
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8pt; }
            img { max-width: 100%; height: auto; }
            .page-break { page-break-before: always; }
          </style>
        </head>
        <body>
          ${content}
        </body>
        </html>
      `
    },

    exportToPDF(content, bookId, totalChapters) {
      const originalTitle = document.title
      try {
        const fileName = `${this.bookTitle}_${new Date().toISOString().slice(0, 10)}`
        document.title = fileName
        let iframe = null
        this.$nextTick(() => {
          try {
            iframe = document.createElement('iframe')
            iframe.style.position = 'absolute'
            iframe.style.width = '0'
            iframe.style.height = '0'
            iframe.style.border = 'none'
            document.body.appendChild(iframe)

            const iframeDoc = iframe.contentWindow.document
            iframeDoc.open()

            iframeDoc.write(`
              <!DOCTYPE html>
              <html>
                <head>
                  <title>${fileName}</title>
                  <style>
                    body {
                      font-size: 14px;
                      color: #000;
                      margin: 0;
                      padding: 0;
                      width: 100%;
                      height: auto;
                      overflow: visible;
                    }
                    img {
                      max-width: 100%;
                      height: auto;
                    }
                    @media print {
                      body {
                        -webkit-print-color-adjust: exact;
                      }
                    }
                  </style>
                </head>
                <body>${content}</body>
              </html>
            `)

            iframeDoc.close()
            iframe.contentWindow.onload = () => {
              iframe.contentWindow.focus()
              iframe.contentWindow.print()
              iframe.contentWindow.onafterprint = () => {
                document.body.removeChild(iframe)
                document.title = originalTitle
              }
            }
          } catch (error) {
            console.error('PDF导出失败:', error)
            if (iframe) document.body.removeChild(iframe)
            document.title = originalTitle
          }
        })
      } catch (error) {
        console.error('导出PDF文档失败:', error)
        document.title = originalTitle
      }
    },

    handleExportDialogClose() {
      console.log('导出弹窗已关闭')
    },
    async _getDigitalBookReviewUserByBook() {
      const { data } = await getDigitalBookReviewUserByBook({ digitalBookReviewId: this.$route.query.id })
      this.reviewList = data
      this.$store.dispatch('user/SetReviewRole', this.reviewList.find(item => item.user.id === this.currentUserId).role)
    },
    fomatterAuthor(role) {
      if (role === 'REVIEW_1') {
        return '一审'
      } else if (role === 'REVIEW_2') {
        return '二审'
      } else if (role === 'REVIEW_3') {
        return '三审'
      } else if (role === 'PROOFREAD') {
        return '校对'
      } else {
        return '审核'
      }
    },
    openReviewRead(item) {
      this.preEditionId = item.id
      this.preShow = true
    },
    timestampToTime(timestamp) {
      timestamp = timestamp || null
      const date = new Date(timestamp)
      const Y = date.getFullYear() + '-'
      const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
      const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
      const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
      const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
      const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
      return Y + M + D + h + m + s
    },

    async _getDigitalBookReviewListByBook() {
      const { data } = await getDigitalBookReviewListByBook({ digitalBookId: this.bookId })
      this.BookReviewList = data
    },
    openMessage(item, refresh = true) {
      let canRefresh = true
      this.bugList.forEach((item1, index) => {
        if (item1.id === item.id) {
          if (this.bugList[index].commentList.length > 0){
            canRefresh = false
          }
        }
      })
      if (!canRefresh && !refresh) return
      getDigitalBugCommentList({ digitalBugId: item.id }).then(res => {
        const data = this.bugList
        data.forEach((item1, index) => {
          if (item1.id === item.id) {
            data[index].commentList = res.data
          }
        })
        this.bugList = data
      })
    },
    submit_message(item) {
      if (this.message === '') {
        this.$message.warning('请输入留言内容')
        return
      }
      submitDigitalBugComment({ digtalBugId: item.id, content: this.message }).then(res => {
        if (res.code === 200) {
          const data = this.bugList
          data.forEach((item1, index) => {
            if (item1.id === item.id) {
              data[index].commentQuantity += 1
            }
          })
          this.bugList = data
          this.bugList = JSON.parse(JSON.stringify(this.bugList))
          this.openMessage(item)
          this.message = ''
        }
      })
    },
    subMitBook() {
      if (window.location.href.indexOf('publisher') > -1) {
        this.$refs.reviewDialog.open({
          title: '确认通过审核？',
          showInput: true,
          placeholder: '请输入审核意见，如没有则不用填写',
          cbs: {
            onSubmit: (content) => {
              // 检查是否是最后一个校对
              const proofreadList = this.reviewList.filter(item => item.role === 'PROOFREAD')
              const otherPassCount = proofreadList.filter(item =>
                item.reviewStatus === 'PASS' &&
                item.user.id !== this.currentUserId
              ).length
              const isLastProofread = proofreadList.length === otherPassCount + 1
              const isCurrentUserReviewing = proofreadList.some(item =>
                item.user.id === this.currentUserId &&
                item.reviewStatus === 'UNDER_REVIEW'
              )

              if (isLastProofread && isCurrentUserReviewing) {
                this.publishInfo = this.bookInfo
                this.$refs.publish.show()
              } else {
                // 普通审核通过流程
                changeDigitalBookReviewStatus({
                  digitalBookReviewId: this.$route.query.id,
                  reviewStatus: 'PASS',
                  reviewOpinion: content || ''
                }).then(res => {
                  if (res.code === 200) {
                    this.$message.success('审核通过')
                    this.$router.go(-1)
                  }
                })
              }
            }
          }
        })
      } else if (window.location.href.indexOf('expert') > -1) {
        this.$refs.reviewDialog.open({
          title: '提示',
          showInput: false,
          cbs: {
            onSubmit: () => {
              changeDigitalBookReviewStatus({
                digitalBookReviewId: this.$route.query.id,
                reviewStatus: 'PASS'
              }).then(res => {
                if (res.code === 200) {
                  this.$message.success('审核通过')
                  this.$router.go(-1)
                }
              })
            }
          }
        })
      }
    },
    setType() {
      if (window.location.href.indexOf('publisher') > -1) {
        this.submitType = '审核通过'
      } else if (window.location.href.indexOf('expert') > -1) {
        this.submitType = '审核通过'
      }
    },
    async closeBug(item) {
      await changeDigitalBugStatus({
        digitalBugId: item.id,
        bugStatus: 'CLOSE'
      })
      await this.getBugList()
      const { data } = await getBookCatalogueByVersion({
        bookId: this.bookId,
        type: 'CHAPTER',
        approvedOnly: false
      }, {
        authorization: this.token
      })
      this.bookTree = data
      const elements = document.getElementById('tinymce_ifr').contentWindow.document.getElementsByClassName('set_review')
      for (let i = 0; i < elements.length; i++) {
        if (elements[i].getAttribute('data-id') === item.dataId) {
          elements[i].removeAttribute('data-id')
        }
      }
      if (item.bugStatus !== 'FIXED') {
        this.saveHtml()
      }
    },
    updateBug(item) {
      const _this = this
      setData({
        keyword: item.text,
        content: item.bugDescription
      })
      addTagModal({
        onSubmit(data) {
          updateDigitalBug({ id: item.id, bugDescription: data.content }).then(res => {
            if (res.code === 200) {
              _this.$message.success(res.message)
              _this.getBugList()
            }
          })
          closeTag()
        }
      })
    },
    Reject() {
      if (window.location.href.indexOf('publisher') > -1) {
        this.$refs.reviewDialog.open({
          title: '确认返修？',
          showInput: true,
          placeholder: '请输入返修意见，如没有则不用填写',
          cbs: {
            onSubmit: (content) => {
              changeDigitalBookReviewStatus({
                digitalBookReviewId: this.$route.query.id,
                reviewStatus: 'FAILED',
                reviewOpinion: content || ''
              }).then(res => {
                if (res.code === 200) {
                  this.$message.success('返修成功')
                  this.$router.go(-1)
                }
              })
            }
          }
        })
      } else if (window.location.href.indexOf('expert') > -1) {
        this.$refs.reviewDialog.open({
          title: '提示',
          showInput: false,
          cbs: {
            onSubmit: () => {
              changeDigitalBookReviewStatus({
                digitalBookReviewId: this.$route.query.id,
                reviewStatus: 'FAILED'
              }).then(res => {
                if (res.code === 200) {
                  this.$message.success('返修成功')
                  this.$router.go(-1)
                }
              })
            }
          }
        })
      }
    },
    flattenDOM(root) {
      const result = []
      const queue = [{ node: root, level: 0, index: 0, parentId: null }]
      let currentId = 0

      while (queue.length > 0) {
        const { node, level } = queue.shift()
        const nodeId = currentId++

        // 提取节点的关键信息
        const nodeInfo = {
          dataId: node.getAttribute('data-id'),
          node
        }

        result.push(nodeInfo)

        // 将子节点加入队列
        const children = Array.from(node.children)
        children.forEach((child, i) => {
          queue.push({
            node: child,
            level: level + 1,
            index: i,
            parentId: nodeId
          })
        })
      }
      return result
    },
    toTop(item) {
      const elements = document.getElementById('tinymce_ifr').contentWindow.document.getElementsByClassName('set_review')
      for (let i = 0; i < elements.length; i++) {
        if (elements[i].getAttribute('data-id') === item.dataId) {
          const e = findParentByClass(elements[i], 'draggable-div')
          let top = elements[i].offsetTop
          if (e) {
            top = e.offsetTop + elements[i].offsetTop
          }
          document.getElementById('tinymce_ifr').contentWindow.scrollTo({
            top: top,
            behavior: 'smooth'
          })
          break
        }
        const list = this.flattenDOM(elements[i])
        for (let j = 0; j < list.length; j++) {
          if (list[j].dataId === item.dataId) {
            const e = findParentByClass(elements[i], 'draggable-div')
            let top = list[j].node.offsetTop
            if (e) {
              top = e.offsetTop + list[j].node.offsetTop
            }
            document.getElementById('tinymce_ifr').contentWindow.scrollTo({
              top: top,
              behavior: 'smooth'
            })
            break
          }
        }
      }
      if (item.postion.indexOf('mceNonEditable') !== -1) {
        const elements = document.getElementById('tinymce_ifr').contentWindow.document.getElementsByClassName('mceNonEditable')
        for (let i = 0; i < elements.length; i++) {
          if ((elements[i].outerHTML).indexOf('图片') !== -1 && (JSON.parse(item.postion).text.indexOf('图片')) !== -1) {
            const arr = this.getSrc(elements[i].outerHTML)
            const arr1 = this.getSrc(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementById('tinymce_ifr').contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('视频') !== -1 && (JSON.parse(item.postion).text.indexOf('视频')) !== -1) {
            const arr = this.getVideoSrc(elements[i].outerHTML)
            const arr1 = this.getVideoSrc(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementById('tinymce_ifr').contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('去做题') !== -1 && (JSON.parse(item.postion).text.indexOf('去做题')) !== -1) {
            const arr = this.getDataId(elements[i].outerHTML)
            const arr1 = this.getDataId(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementById('tinymce_ifr').contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('操作实训') !== -1 && (JSON.parse(item.postion).text.indexOf('操作实训')) !== -1) {
            const arr = this.getDataId(elements[i].outerHTML)
            const arr1 = this.getDataId(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementById('tinymce_ifr').contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('附件') !== -1 && (JSON.parse(item.postion).text.indexOf('附件')) !== -1) {
            const arr = this.getSrc(elements[i].outerHTML)
            const arr1 = this.getSrc(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementById('tinymce_ifr').contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if (elements[i].className.includes('tips_item') && JSON.parse(item.postion).text.includes('tips_item')) {
            if (this.htmlContentsEqual(elements[i].outerHTML, JSON.parse(item.postion).text)) {
              const top = elements[i].offsetTop
              document.getElementById('tinymce_ifr').contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
        }
      }
      const mathsElements = document.getElementById('tinymce_ifr').contentWindow.document.getElementsByClassName('math-tex')
      for (let i = 0; i < mathsElements.length; i++) {
        if (item.text.includes(mathsElements[i].getAttribute('data-latex'))) {
          const top = mathsElements[i].offsetTop
          document.getElementById('tinymce_ifr').contentWindow.scrollTo({
            top: top,
            behavior: 'smooth'
          })
          break
        }
      }
      if (this.isTable(JSON.parse(item.postion).text)) {
        const elements = document.getElementById('tinymce_ifr').contentWindow.document.getElementsByTagName('table')
        for (let i = 0; i < elements.length; i++) {
          if (this.compareTableWithElement(JSON.parse(item.postion).text, elements[i])) {
            const top = elements[i].offsetTop
            document.getElementById('tinymce_ifr').contentWindow.scrollTo({
              top: top,
              behavior: 'smooth'
            })
            break
          }
        }
      }
    },
    isTable(html) {
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = html
      const tableFromHtml = tempDiv.querySelector('table')

      // 检查是否找到表格
      if (!tableFromHtml) {
        return false
      }
      return true
    },
    // 比较表格
    compareTableWithElement(htmlTable, targetElement) {
      // 创建临时元素解析 HTML 表格字符串
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlTable
      const tableFromHtml = tempDiv.querySelector('table')
      // 检查是否找到表格
      if (!tableFromHtml) {
        return false
      }

      // 获取两个表格的所有行
      const rowsFromHtml = Array.from(tableFromHtml.querySelectorAll('tr'))
      const rowsFromElement = Array.from(targetElement.querySelectorAll('tr'))

      // 比较行数
      if (rowsFromHtml.length !== rowsFromElement.length) {
        return false
      }
      // 逐行比较
      for (let i = 0; i < rowsFromHtml.length; i++) {
        const cellsFromHtml = Array.from(rowsFromHtml[i].querySelectorAll('td, th'))
        const cellsFromElement = Array.from(rowsFromElement[i].querySelectorAll('td, th'))

        // 比较单元格数量
        if (cellsFromHtml.length !== cellsFromElement.length) {
          return false
        }

        // 逐单元格比较内容
        for (let j = 0; j < cellsFromHtml.length; j++) {
          const htmlCellContent = cellsFromHtml[j].textContent.trim()
          const elementCellContent = cellsFromElement[j].textContent.trim()

          // 比较单元格内容（忽略空格差异）
          if (htmlCellContent !== elementCellContent) {
            return false
          }
        }
      }
      // 如果所有检查都通过，则内容相同
      return true
    },
    // 比较气泡
    htmlContentsEqual(html1, html2) {
      const div1 = document.createElement('div')
      const div2 = document.createElement('div')
      div1.innerHTML = html1
      div2.innerHTML = html2

      // 获取tips_item中的可见文本（不包含子元素）
      function getVisibleText(div) {
        const tipsItem = div.querySelector('.tips_item')
        if (!tipsItem) return ''
        return Array.from(tipsItem.childNodes)
          .filter(node => node.nodeType === Node.TEXT_NODE)
          .map(node => node.textContent.trim())
          .join('')
      }

      // 获取tips_content中的隐藏文本
      function getHiddenText(div) {
        const tipsContent = div.querySelector('.tips_content')
        return tipsContent ? tipsContent.textContent.trim() : ''
      }

      const visible1 = getVisibleText(div1)
      const visible2 = getVisibleText(div2)
      const hidden1 = getHiddenText(div1)
      const hidden2 = getHiddenText(div2)

      return visible1 === visible2 && hidden1 === hidden2
    },
    getDataId(val) {
      const content = val
      const imgReg = /<div.*?(?:>|\/>)/gi
      const srcReg = /data-id=[\'\"]?([^\'\"]*)[\'\"]?/i
      const arr = content.match(imgReg)
      const srcList = []
      if (arr != null) {
        for (let i = 0; i < arr.length; i++) {
          const src = arr[i].match(srcReg)
          if (src != null) {
            srcList.push(src[1])
          }
        }
        return srcList
      }
      return []
    },
    getVideoSrc(val) {
      const content = val
      const videoReg = /<video.*?(?:>|\/>)/gi
      const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i
      const arr = content.match(videoReg)
      const srcList = []
      if (arr != null) {
        for (let i = 0; i < arr.length; i++) {
          const src = arr[i].match(srcReg)
          srcList.push(src[1])
        }
        return srcList
      }
      return []
    },
    getSrc(val) {
      const content = val
      const imgReg = /<img.*?(?:>|\/>)/gi
      const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i
      const arr = content.match(imgReg)
      const srcList = []
      if (arr != null) {
        for (let i = 0; i < arr.length; i++) {
          const src = arr[i].match(srcReg)
          srcList.push(src[1])
        }
        return srcList
      }
      return []
    },
    async deleteBug(item) {
      this.$confirm('是否要删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        await changeDigitalBugStatus({
          digitalBugId: item.id,
          bugStatus: 'CLOSE'
        })
        await this.getBugList()
        const { data } = await getBookCatalogueByVersion({
          bookId: this.bookId,
          type: 'CHAPTER',
          approvedOnly: false
        }, {
          authorization: this.token
        })
        this.bookTree = data
        const elements = document.getElementById('tinymce_ifr').contentWindow.document.getElementsByClassName('set_review')
        for (let i = 0; i < elements.length; i++) {
          console.log(elements[i].getAttribute('data-id'))
          if (elements[i].getAttribute('data-id') === item.dataId) {
            elements[i].removeAttribute('data-id')
            elements[i].removeAttribute('data-mce-style')
            elements[i].style.color = ''
            elements[i].style.border = 'none'
          }
        }
        this.saveHtml()
      })
    },
    formatType(type) {
      if (type === 'OPEN') {
        return 'info'
      }
      if (type === 'FIXED') {
        return 'danger'
      }
    },

    formatTypeText(type) {
      if (type === 'OPEN') {
        return '待修改'
      }
      if (type === 'FIXED') {
        return '待复核'
      }
    },
    async getBugList() {
      const { data } = await getDigitalBugList({ digitalBookId: Number(this.bookId), catalogueId: this.selectTreeId })
      data.forEach(item => {
        item.dataId = JSON.parse(item.postion).id
        item.text = JSON.parse(item.postion).text
        item.commentList = []
      })
      this.bugList = data
      this.$nextTick(() => {
        // 渲染所有包含公式的元素
        const mathElements = document.querySelectorAll('.math-tex')
        if (mathElements.length > 0) {
          window.MathJax.typesetPromise(mathElements).catch((err) => {
            console.error('MathJax typeset error:', err)
          })
        }
      })
    },
    showtips(e) {
      const selectContent = tinymce.activeEditor.selection.getContent()
      if (!selectContent) {
        this.showTag = false
        return
      }
      this.showTag = true
      const item = e.target
      const view = document.getElementById('tinymce_ifr')
      let y = item.getBoundingClientRect().top + view.getBoundingClientRect().top
      if (window.innerHeight - e.pageY < 195) {
        y = window.innerHeight - 200
      }
      this.tagPosition = {
        top: y,
        left: item.getBoundingClientRect().left + view.getBoundingClientRect().left
      }
    },
    initFun () {
      if (!document.getElementById('tinymce_ifr')) return
      document.getElementById('tinymce_ifr').contentWindow.document.addEventListener('click', this.richEvent, false)
      document.getElementById('tinymce_ifr').contentWindow.document.addEventListener('click', this.showImageList, false)
      document.getElementById('tinymce_ifr').contentWindow.document.addEventListener('mouseup', this.showtips, false)
      document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.addEventListener('wheel', this.setPage, false)
      document.addEventListener('click', this.closePop)
    },
    setPage(event) {
      if (this.isLoading) {
        return
      }
      const container = document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.documentElement
      const scrollTop = container.scrollTop // 当前滚动位置
      const scrollHeight = container.scrollHeight // 内容总高度
      const clientHeight = container.clientHeight // 可视区域高度
      // console.log(scrollTop, scrollHeight, clientHeight)
      // console.log(event)
      if (event.deltaY > 0) { // 向下滚动
        // 如果已经在底部，加载下一章
        if (scrollTop + clientHeight >= scrollHeight) {
          this.loadNextChapter()
        }
      } else if (event.deltaY < 0) { // 向上滚动
        // 如果已经在顶部，加载上一章
        if (scrollTop === 0) {
          this.loadPreviousChapter()
        }
      }
    },
    async loadPreviousChapter() {
      console.log('加载上一章')
      const preNode = this.findPreviousNode(this.bookTree, this.selectTreeId)
      if (preNode) {
        this.isLoading = true
        await this.handleNodeClick(preNode)
        setTimeout(() => {
          this.isLoading = false
        }, 1000)
      }
    },

    // 加载下一章
    async loadNextChapter() {
      console.log('加载下一章')
      const nextNode = this.findNextNode(this.bookTree, this.selectTreeId)
      if (nextNode) {
        this.isLoading = true
        await this.handleNodeClick(nextNode)
        setTimeout(() => {
          this.isLoading = false
        }, 1000)
      }
    },
    findNextNode(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i + 1] || null
        }
      }

      return null
    },
    findPreviousNode(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i - 1] || null
        }
      }

      return null
    },
    showImageList (e) {
      if (e.target.classList.contains('img_card_button')) {
        this.imgListInfo = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.imgs.open()
      }
      if (e.target.classList.contains('video_button')) {
        this.videoInfo = {
          src: e.target.src,
          poster: e.target.poster,
          text: e.target.parentNode.getElementsByTagName('p')[1].innerText
        }
        this.$refs.videoCard.open()
      }
    },
    richEvent (e) {
      this.$refs.tips.close()
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        const view = document.getElementById('tinymce_ifr')
        let y = item.getBoundingClientRect().top + view.getBoundingClientRect().top + 20
        if (window.innerHeight - e.pageY < 195) {
          y = window.innerHeight - 200
        }
        this.tipsPositon = {
          top: y,
          left: item.getBoundingClientRect().left + view.getBoundingClientRect().left
        }
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.show()
      }
      if (e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          const url = e.target.parentNode.children[1].innerText
          const fileName = e.target.parentNode.children[2].innerText
          const notif = Notification({
            title: fileName,
            dangerouslyUseHTMLString: true,
            message: '',
            duration: 0
          })
          throttle(function () {
            const xhr = new XMLHttpRequest()
            xhr.open('get', url)
            xhr.responseType = 'blob'
            xhr.addEventListener('progress', (e) => {
              const complete = Math.floor(e.loaded / e.total * 100)
              notif.message = complete + '%'
              if (complete >= 100) {
                notif.close()
              }
            })
            xhr.send()
            xhr.onload = function () {
              if (this.status === 200 || this.status === 304) {
                const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
                saveAs(blob, fileName)
              }
            }
          }, 2000)
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.doTest.open()
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
    },
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      if (this.getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.imgs.open()
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    async _takeBook() {
      // 判断是否有人正在使用该书
      const { data } = await takeBook({
        bookId: this.bookId,
        scene: 'BOOK_CATALOGUE_OWN',
        catalogueId: this.selectTreeId
      }, {
        authorization: this.token
      })
      if (!data) {
        this.otherUse = true
        // this.editor.blur()
      } else {
        if (this.otherUse) {
          this._getContentEditor(this.selectTreeId)
        }
        this.otherUse = false
        // this.editor.enable()
      }
    },
    async _getBook() {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN',
          digitalBookReviewId: this.$route.query.id
        }, {
          authorization: this.token
        })
        this.bookInfo = data
        this.bookTitle = data.title
      }
    },
    async _getBookCatalogue() {
      const { data } = await getBookCatalogueByVersion({
        bookId: this.bookId,
        type: 'CHAPTER',
        approvedOnly: false
      }, {
        authorization: this.token
      })
      this.bookTree = data
    },
    onCreated(editor) {
      // window.editor = this.editor
      // window.slateTransforms = this.slateTransforms
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      if (!this.selectTreeId) {
        editor.disable()
      }
    },
    getHtml() {
      return tinymce.activeEditor.getContent()
    },
    async handleNodeClick(nodeData) {
      this.loading = true
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
      this.preNode = nodeData
      this.selectTreeId = nodeData.id
      this.currentKey = nodeData.id
      localStorage.setItem('review_selectTreeId', nodeData.id)

      // 判断是否加锁
      if (this.keepHeartTime) {
        clearInterval(this.keepHeartTime)
        this.keepHeartTime = null
      }
      this._takeBook()
      this.keepHeartTime = setInterval(() => {
        this._takeBook()
      }, 10000)
      // 重置html内容避免编辑器报错
      // this.loading = true
      this.catalogueId = 0
      this.htmlId = 0
      this.html = '<p><br></p>'
      this.htmlTemp = null
      setTimeout(async () => {
        await this._getContentEditor(nodeData.id)
        this.$nextTick(() => {
          this.$refs.treeSelect.setCurrentKey(this.currentKey)
        })
        this.loading = false
        this.contentChanged = false
        this.getBugList()

        // setTimeout(() => {
        //   this.initFun()
        // }, 1000)
      }, 1000)
    },
    async _getContentEditor(id) {
      if (this.selectTreeId === id) {
        const { data } = await getContentEditor({
          catalogueId: id
        }, {
          authorization: this.token
        })
        if (data && data.length > 0) {
          this.htmlId = data[0].id
          this.html = data[0].data
          // this.editor.setHtml(data[0].data)
        } else {
          this.htmlId = 0
          this.html = '<p><br></p>'
          // this.editor.setHtml('<p>编辑器创建时的默认内容。</p>')
        }
        this.catalogueId = id
        this.htmlTemp = this.html
      }
    },
    handleDragStart(node, ev) {
      console.log('drag start', node)
    },
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      // console.log('tree drop: ', draggingNode.data, dropNode.data, dropType)
      await dragCatalogue({
        catalogueId: draggingNode.data.id,
        referCatalogueId: dropNode.data.id,
        position: dropType.toUpperCase()
      }, {
        authorization: this.token
      })
      this._getBookCatalogue()
    },
    eidtDone(data) {
      this.editChapterShow = false
      this._getBookCatalogue()
      // if (this.currChapter.data) {
      //   // 编辑
      //   this.treeData.title = data.title
      //   // this._getContentEditor(this.treeData.id)
      // } else {
      //   // 新增
      //   if (!this.currChapter.parentId) {
      //     // 根目录
      //     this.bookTree.push(data)
      //   } else {
      //     const newChild = data
      //     this.treeData.childCatalogue.push(newChild)
      //   }
      // }
    },
    treeAppend(node, data) {
      this.treeData = data
      if (node) {
        this.currChapter = { bookId: this.bookId, parentId: data.id, data: null }
      } else {
        this.currChapter = { bookId: this.bookId, parentId: 0, data: null }
      }
      this.editChapterShow = true
    },
    treeEdit(node, data) {
      this.treeData = data
      this.currChapter = { bookId: this.bookId, parentId: data.parentId, data }
      this.editChapterShow = true
    },
    async treeRemove(node, data) {
      try {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await deleteBookCatalogue({
            catalogueId: data.id
          }, {
            authorization: this.token
          })
          this._getBookCatalogue()
          // const parent = node.parent
          // const children = parent.data.childCatalogue || parent.data
          // const index = children.findIndex(d => d.id === data.id)
          // children.splice(index, 1)
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    async saveHtml(type = false) {
      if (this.selectTreeId && !this.loading && this.catalogueId !== 0 && !this.otherUse && this.selectTreeId === this.catalogueId) {
        const obj = {
          data: this.getHtml(),
          catalogueId: this.selectTreeId
        }
        if (obj.data === this.htmlTemp) {
          console.log('文本内容一样，不提交')
          return
        }
        if (this.htmlId) {
          obj.id = this.htmlId
        }
        const { data } = await saveContent(obj, {
          authorization: this.token
        })
        console.log('保存成功')
        if (type) {
          this.$message.success('保存成功')
        }
        this.htmlTemp = obj.data
        this.htmlId = data
      }
    },
    onFocus(editor) {
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
      // this.saveTimer = setInterval(async () => {
      //   // if (!this.contentChanged) return
      //   // if (!this.editor.isFocused()) {
      //   //   clearInterval(this.saveTimer)
      //   //   this.saveTimer = null
      //   //   return
      //   // }
      //   this.autoTips = true
      //   // await this.saveHtml()
      //   this.autoTips = false
      //   this.contentChanged = false
      // }, 5000)
    },
    onBlur(editor) {
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
    },
    onChange(editor) {
      this.contentChanged = true
    },
    resetHtml() {
      // 使用原生slate删除表格内容
      const content = <p></p>
      tinymce.editor.setContent(content)
    },
    beforeunloadFn(e) {
      if (this.getHtml() !== this.htmlTemp) {
        // this.saveHtml()
        // console.log("文本内容一样，不提交")
        e.returnValue = ('确定离开当前页面吗？')
        return '有未保存的编辑'
      }
    },
    preShowFn() {
      this.preShow = true
      // this.saveHtml()
    },
    getStatusClass(status) {
      switch (status) {
        case 'PASS':
          return 'status-completed'
        case 'UNDER_REVIEW':
          return 'status-pending'
        case 'FAILED':
          return 'status-reviewed'
        default:
          return ''
      }
    },
    viewReview(item) {
      // 查看审核意见的处理逻辑
      this.activeName = 'first'
      // 可以根据需要过滤或处理特定审核人的意见
      const reviewerComments = this.bugList.filter(bug =>
        bug.user.displayName === item.name
      )
      if (reviewerComments.length > 0) {
        // 如果有评论，可以滚动到第一条评论
        this.$nextTick(() => {
          const element = document.querySelector('.bug_item')
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' })
          }
        })
      }
    },
    formatStatus(status) {
      switch (status) {
        case 'PASS':
          return '已通过'
        case 'UNDER_REVIEW':
          return '审核中'
        case 'FAILED':
          return '已返修'
        default:
          return '未知状态'
      }
    },
    getCurrentUserId() {
      return this.id
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-collapse-item__content{
  font-size: 8px !important;
  position: relative;
  .submit_message{
    width: 100%;
    height: 30px;
    position: absolute;
    bottom:0;
    left:0;
    display: flex;
    justify-content: space-between;
   .message_input{
     width: 240px;
    height: 20px !important;
      font-size: 8px !important;
    padding: 2px;
    .el-input__inner{
    width: 100%;
    height: 20px !important;
  }
}
.message_button{
  width: 40px;
  height: 20px;
  margin-top: 2px;
  font-size: 8px !important;
  padding: 5px;
}
  }
  .message_content{
    width: 100%;
    margin-bottom: 10px;
    .message_item{
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 6px 8px;
      margin: 4px 0;

       .header{
        width: 100%;
        display: flex;
        font-size: 8px !important;
        margin-bottom: 4px;
        align-items: center;
        .message_tag{
          font-size: 6px;
          height: 14px;
          padding: 2px;
        }
       }
       .message_content{
        font-size: 10px;
        line-height: 1.4;
        color: #555;
       }
    }
  }
}
::v-deep .el-collapse-item__header{
  font-size: 8px !important;
  height: 20px;
  position: relative;
  .add{
    position: absolute;
    color: #2F80ED;
    left: 200px;
  }
}
.right_content {
  width: 350px;
  height: 100%;
  padding: 10px;
  background: #fff;
  border-left: 1px solid #E0E0E0;
  overflow: auto;
  .review_item{
    width: 100%;
    height: 50px;
    position: relative;
    .title{
      font-size: 12px;
      text-align: left;
      font-weight: 500;
    }
    .time{
      font-size: 10px;
      text-align: left;
      color: #828282;
    }
    .open{
      width: 50px;
      position: absolute;
      right: 20px;
      font-size: 10px;
      padding: 5px;
      top:10px;
    }
  }
::v-deep .el-tabs__header{
  transform: scale(0.8);
}
::v-deep .el-tabs__nav-scroll{
  // width: 50%!important;
  margin: 0 auto!important;
}

  .title {
    width: 100%;
    text-align: center;
  }

  .bug_item {
    margin: 12px 0;
    font-size: var(--font-size-M);
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
      border-color: #d9d9d9;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    }

    .bug_item_header{
      display: flex;
      justify-content:flex-start;
      gap: 5px;
      align-items: center;
      white-space: nowrap;
      margin-bottom: 8px;

      .bug_item_header_author_container{
        display: flex;
        gap: 5px;
        align-items: center;
        white-space: nowrap;
      }
      .bug_item_header_author{
        width: 20px;
        text-align: center;
        border-radius: 10px;
        font-size: 9px;
        height: 20px;
        line-height: 20px;
        white-space: nowrap;
        background: #2F80ED;
        color: #fff;
      }
      .review_1{
        background: #00C7BE;
        color: #fff;
      }
      .review_2{
        background: #FF9500;
        color: #fff;
      }
      .review_3{
        background: #9B51E0;
        color: #fff;
      }
      .proofread{
        background: #2F80ED;
        color: #fff;
      }
      .review_t{
        color: #2F80ED;
      }
      .review_1_t{
        color: #00C7BE;
      }
      .review_2_t{
        color: #FF9500;
      }
      .review_3_t{
        color: #9B51E0;
      }
      .proofread_t{
        color: #2F80ED;
      }
    }
    ::v-deep .el-tag {
      font-size: 8px;
      padding: 5px;
      height: 20px;
      line-height: 10px;
    }

    ::v-deep .el-button {
      font-size: 10px;
      // color:red
    }
    ::v-deep .el-collapse {
      border-top: none !important;
      border: none !important;
      margin-top: 8px;

      .el-collapse-item {
        border: none !important;

        .el-collapse-item__header {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 8px 12px;
          font-size: 11px;
          color: #666;

          .add {
            color: #409eff;
            margin-left: 8px;
            cursor: pointer;

            &:hover {
              color: #66b1ff;
            }
          }
        }

        .el-collapse-item__content {
          border: 1px solid #e9ecef;
          border-top: none;
          border-radius: 0 0 4px 4px;
          background: #fff;
          padding: 8px 12px;
        }
      }
    }
    .bug_content {
      width: 100%;
      margin-top: 8px;
      padding: 8px 0;
      font-size: 13px;
      line-height: 1.5;
      color: #333;
    }

    .bug_text {
      background: #f0f0f0;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 8px;
      margin: 6px 0;
      font-size: 11px;
      color: #666;
      overflow: hidden;
      cursor: pointer;

      div {
        width: 100%;
        text-overflow: ellipsis;
      }

      ::v-deep img {
        width: 100%;
      }
    }
  }
}

#tinymce {
  img {
    width: 100%;
  }
}

.editor-dig {
  width: 100%;
  height: 100%;
  background: #FFF;
  box-sizing: border-box;
  position: relative;

  .word_button {
    width: 82px;
    height: 30px;
    margin-left: 800px;
    cursor: pointer;
  }

  .other-use {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: #000000, $alpha: .4);
    z-index: 999999;
  }

  .dig-head {
    width: 100%;
    min-width: 1060px;
    overflow-x: auto;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    box-sizing: border-box;
    border-bottom: 1px solid #c9c2c2;
    ::v-deep .el-button {
      font-size: 15px;
      padding: 10px;
      // color:red
    }
    .dig-tips {
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .dig-btn {
      border-radius: 4px;
      background: #2F80ED;
      color: #FFF;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 5px 10px;
      box-sizing: border-box;
      cursor: pointer;
      margin-left: 10px;
    }
  }

  .dig-box {
    width: 100%;
    min-width: 1060px;
    height: calc(100% - 50px);
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #E0E0E0;
    background: #F9F9F9;
    overflow: hidden;
  }

  .dig-left {
    width: 260px;
    height: 100%;
    background: #fff;
    border-right: 1px solid #E0E0E0;
    overflow-x: hidden;

    .chapter-title {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 5px;

      .icon1 {
        color: #000;
        font-size: 16px;
        display: flex;
        align-items: center;

        img {
          width: 27px;
          height: 27px;
          margin-right: 5px;
        }
      }

      .add-btn {
        color: #2F80ED;
        font-size: 14px;
        margin-right: 10px;
        cursor: pointer;
      }
    }

    .chapter-body {
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      overflow-x: hidden;

      ::v-deep .el-tree-node__content {
        height: 40px;
      }

      ::v-deep .el-button+.el-button {
        margin-left: 5px;
      }

      ::v-deep .el-button {
        font-size: 14px;
      }

      ::v-deep .el-tree-node__content>.el-tree-node__expand-icon {
        padding: 5px;
      }

      .tree-body {
        width: calc(100% - 30px);
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;

        .chapter-name {
          flex: 1;
          overflow: hidden;
          @include scrollBar;
        }

        .chapter-option {
          margin-right: 20px;
          width: 15px;
          height: 15px;
          font-size: 12px;
          font-weight: 700;
          border-radius: 10px;
          background: red;
          color: #fff;
          text-align: center;
          line-height: 15px;
          flex-shrink: 0;
        }
      }
    }
  }

  .dig-center {
    width: 793.667px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #FFF;
    position: relative;

    .toolbar {
      width: 793.667px;
      height: 80px;
    }

    .eidtor {
      width: 793.667px;
      height: 100%;
      padding: 10px;
      overflow-y: auto;
      position: relative;
      .tag_menu{
        width: 50px;
        height: 30px;
        text-align: center;
        font-size: 12px;
        line-height: 30px;
        background:#fff ;
        position: absolute;
        border: 2px solid #f2f2f2;
        z-index: 11;
        cursor: pointer;
      }
      .tag_menu:hover{
        background: #76c4dc;
      }
    }
  }

  ::v-deep .w-e-bar-divider {
    display: none;
  }

  ::v-deep .w-e-textarea-video-container {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;

    video {
      width: 100%;
    }
  }

  .pre {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    overflow: auto;
  }
}

.review-detail {
  padding: 10px;
  .review-item {
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
    .reviewer {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 10px;
      .left-content {
        display: flex;
        align-items: center;
        width: 120px;
        .name {
          margin-right: 5px;
        }
      }
      .right-content {
        width: 100px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .status-wrapper {
          display: flex;
          align-items: center;
          width: 100px;
          .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 8px;
          }
          .status-pending {
            background-color: #409EFF;
          }
          .status-reviewed {
            background-color: #F56C6C;
          }
          .status-completed {
            background-color: #67C23A;
          }
          .status {
            color: #909399;
            font-size: 10px;
          }
        }
        .view-btn {
          padding: 0;
          font-size: 10px;
          color: #2F80ED;
        }
      }
    }
  }
}

::v-deep .review-detail-popover {
  padding: 12px;
  min-width: 200px;
}

.review-opinion {
  padding: 8px;
  .opinion-title {
    font-size: 10px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
  }
  .opinion-content {
    font-size: 10px;
    color: #666;
    word-break: break-all;
    white-space: pre-wrap;
  }
}

::v-deep .custom-confirm-box {
  .el-message-box__input {
    textarea {
      height: 100px;
      resize: none;
    }
  }
}

.bug_list_header {
  display: flex;
  padding: 10px 0;
  margin-bottom: 10px;

  div {
    flex: 1;
    text-align: center;
    cursor: pointer;
    color: #666;
    font-size: 10px;
    position: relative;
    padding: 0 15px;

    &.active {
      color: #2F80ED;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background-color: #2F80ED;
      }
    }

    &:hover {
      color: #2F80ED;
    }
  }
}
</style>
