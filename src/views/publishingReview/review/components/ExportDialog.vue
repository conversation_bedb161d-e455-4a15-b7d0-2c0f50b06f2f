<template>
  <NormalDialog
    v-if="dialogVisible"
    width="100%"
    title="导出选项"
    :dialog-visible="dialogVisible"
    :is-center="true"
    :show-close="false"
    @closeDialog="handleClose"
  >
    <div class="export-container">
      <div ref="content" class="rich-text-container">
        <!-- 内容加载时的全屏loading -->
        <div v-if="loading && !isExporting && !selectedContent" class="loading-container">
          <div class="loading-content">
            <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
            <p style="margin-top: 15px; color: #666;">
              {{ loadingText || '正在加载内容...' }}
            </p>
            <!-- 内容加载时显示进度条 -->
            <div v-if="totalRequests > 0" class="progress-info">
              <el-progress
                :percentage="loadingProgress"
                :stroke-width="6"
                :show-text="true"
              />
              <p style="margin-top: 10px; font-size: 12px; color: #999;">
                已完成 {{ completedRequests }}/{{ totalRequests }} 个请求
              </p>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="selectedContent===''" class="emty">
          <Empty description="暂无数据" />
        </div>

        <!-- 内容展示 -->
        <div
          v-else
          ref="richText"
          class="rich-text-content"
          v-html="selectedContent"
        ></div>

        <!-- 导出时的蒙层loading -->
        <div v-if="isExporting" class="export-overlay">
          <div class="export-loading">
            <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
            <p style="margin-top: 15px; color: #666;">正在导出...</p>
          </div>
        </div>
      </div>

      <!-- 固定在底部的按钮区域 -->
      <div class="export-footer">
        <div class="export-actions">
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
          >
            <div slot="content" style="width:30vw;">
              <p>在macOS系统上，导出时中文文字丢失通常是由于系统字体设置问题导致的‌。</p>
              <p>解决方法：打开macOS系统的访达，搜索"字体册"...</p>
            </div>
            <span class="tips">文字丢失<i class="el-icon-question"></i></span>
          </el-tooltip>

          <div class="action-buttons">
            <el-button
              class="export-btn cancel-btn"
              type=""
              @click="close"
            >
              取消
            </el-button>
            <el-button
              class="export-btn"
              type="primary"
              :disabled="loading || selectedContent === '' || isExporting"
              @click="printContent"
            >
              导出
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import Empty from '@/components/classPro/Empty/index.vue'
import { getContent, getBookCatalogue } from '@/api/digital-api.js'
import { getDigitalBugList } from '@/api/publishing.js'

export default {
  name: 'ExportDialog',
  components: { NormalDialog, Empty },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 从父组件传入的书籍信息
    bookId: {
      type: [String, Number],
      default: ''
    },
    digitalBookReviewId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      selectedContent: '', // 最终展示的完整内容
      loading: false, // 加载状态
      loadingText: '正在加载目录...', // 动态加载提示文本
      isExporting: false, // 导出状态

      // 数据存储
      catalogueData: [], // 目录数据
      contentData: {}, // 章节内容数据 {catalogueId: content}
      reviewData: {}, // 审核意见数据 {catalogueId: reviews}
      allChapterIds: [], // 所有章节ID列表

      // 统计信息
      totalChapters: 0, // 总章节数
      loadedChapters: 0, // 已加载章节数
      totalRequests: 0, // 总请求数
      completedRequests: 0, // 已完成请求数

      // 错误处理
      errors: [], // 错误信息收集

      // 性能控制
      batchSize: 5, // 批次大小，控制并发请求数量
      maxRetries: 3, // 最大重试次数

      // 导出配置
      exportFormat: 'word', // 导出格式：word/pdf
      includeReviews: true, // 是否包含审核意见
      includeImages: true // 是否包含图片
    }
  },
  computed: {
    dialogVisible() {
      return this.visible
    },
    // 计算加载进度
    loadingProgress() {
      if (this.totalRequests === 0) return 0
      return Math.round((this.completedRequests / this.totalRequests) * 100)
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 弹窗打开时立即显示，然后开始加载内容
        this.$nextTick(() => {
          this.initializeData()
        })
      }
    }
  },
  methods: {
    // 初始化数据并开始加载完整内容
    async initializeData() {
      console.log('🚀 ExportDialog初始化参数:', {
        bookId: this.bookId,
        digitalBookReviewId: this.digitalBookReviewId,
        includeReviews: this.includeReviews
      })

      this.resetData()
      // 延迟一下，确保弹窗已经完全显示
      setTimeout(() => {
        this.loadCompleteBookContent()
      }, 100)
    },

    // 重置所有数据
    resetData() {
      this.selectedContent = ''
      this.loading = false
      this.isExporting = false
      this.loadingText = '正在加载目录...'
      this.catalogueData = []
      this.contentData = {}
      this.reviewData = {}
      this.totalChapters = 0
      this.loadedChapters = 0
      this.totalRequests = 0
      this.completedRequests = 0
      this.errors = []
    },

    // 加载完整的书籍内容（目录 + 章节内容 + 审核意见）
    async loadCompleteBookContent() {
      if (!this.bookId) {
        this.$message.error('缺少书籍ID，无法加载内容')
        return
      }

      try {
        this.loading = true
        this.loadingText = '正在加载目录结构...'

        // 1. 获取目录结构
        await this.loadCatalogueData()

        // 2. 批量获取章节内容和审核意见
        await this.loadAllChapterData()

        // 3. 组装最终内容（包含MathJax渲染）
        await this.assembleCompleteContent()

        // 终端日志记录加载完成信息
        console.log(`✅ 内容加载完成`, {
          totalChapters: this.totalChapters,
          loadedChapters: this.loadedChapters,
          totalRequests: this.totalRequests,
          completedRequests: this.completedRequests,
          errors: this.errors.length
        })
      } catch (error) {
        console.error('加载完整内容失败:', error)
        this.$message.error('加载内容失败：' + error.message)
        this.selectedContent = this.generateErrorContent(error)
      } finally {
        this.loading = false
        this.loadingText = '加载完成'
      }
    },

    // 获取目录数据 - 使用你提供的API
    async loadCatalogueData() {
      const { data } = await getBookCatalogue({
        bookId: this.bookId,
        type: 'CHAPTER',
        approvedOnly: true // 只获取已审核通过的章节
      })

      if (data && data.length) {
        this.catalogueData = data
        // 收集所有叶子节点（实际章节）
        this.allChapterIds = this.collectAllChapterIds(data)
        this.totalChapters = this.allChapterIds.length
        this.totalRequests = this.totalChapters * (this.includeReviews ? 2 : 1)
        this.loadingText = `发现 ${this.totalChapters} 个章节，准备加载内容...`

        console.log('目录数据加载完成:', {
          totalChapters: this.totalChapters,
          chapterIds: this.allChapterIds
        })
      } else {
        throw new Error('未找到目录数据')
      }
    },

    // 递归收集所有章节ID（叶子节点）
    collectAllChapterIds(catalogues) {
      let chapterIds = []
      catalogues.forEach(item => {
        if (item.childCatalogue && item.childCatalogue.length > 0) {
          // 有子章节，递归收集
          chapterIds = chapterIds.concat(this.collectAllChapterIds(item.childCatalogue))
        } else {
          // 叶子节点，是实际的章节
          chapterIds.push({
            id: item.id,
            title: item.title,
            parentTitle: item.parentTitle || ''
          })
        }
      })
      return chapterIds
    },

    // 批量加载所有章节数据（内容 + 审核意见）
    async loadAllChapterData() {
      this.loadingText = '正在批量加载章节内容...'

      // 使用批次处理，避免并发请求过多导致浏览器崩溃
      const batches = this.createBatches(this.allChapterIds, this.batchSize)

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i]
        this.loadingText = `正在加载第 ${i + 1}/${batches.length} 批章节数据...`

        await this.processBatch(batch)

        // 批次间稍作延迟，避免服务器压力过大
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200))
        }
      }
    },

    // 创建批次
    createBatches(items, batchSize) {
      const batches = []
      for (let i = 0; i < items.length; i += batchSize) {
        batches.push(items.slice(i, i + batchSize))
      }
      return batches
    },

    // 处理单个批次
    async processBatch(chapterBatch) {
      console.log(`📦 处理批次，包含 ${chapterBatch.length} 个章节:`, chapterBatch)

      const promises = []

      // 为每个章节创建内容和审核意见的请求
      chapterBatch.forEach(chapter => {
        console.log(`🔄 开始处理章节:`, {
          id: chapter.id,
          title: chapter.title,
          includeReviews: this.includeReviews
        })

        // 获取章节内容
        promises.push(
          this.loadChapterContent(chapter.id)
            .then(content => {
              console.log(`✅ 章节 ${chapter.id} 内容加载成功，长度: ${content.length}`)
              this.contentData[chapter.id] = content
              this.completedRequests++
              this.loadedChapters++
            })
            .catch(error => {
              console.error(`❌ 加载章节 ${chapter.id} 内容失败:`, error)
              this.errors.push(`章节 "${chapter.title}" 内容加载失败: ${error.message}`)
              this.contentData[chapter.id] = `<p style="color: red;">内容加载失败: ${error.message}</p>`
              this.completedRequests++
            })
        )

        // 获取审核意见（如果需要）
        if (this.includeReviews) {
          console.log(`📝 开始加载章节 ${chapter.id} 的审核意见...`)
          promises.push(
            this.loadChapterReviews(chapter.id)
              .then(reviews => {
                console.log(`✅ 章节 ${chapter.id} 审核意见加载成功:`, reviews)
                this.reviewData[chapter.id] = reviews
                this.completedRequests++
              })
              .catch(error => {
                console.error(`❌ 加载章节 ${chapter.id} 审核意见失败:`, error)
                this.errors.push(`章节 "${chapter.title}" 审核意见加载失败: ${error.message}`)
                this.reviewData[chapter.id] = []
                this.completedRequests++
              })
          )
        } else {
          console.log(`⏭️ 跳过章节 ${chapter.id} 的审核意见加载（includeReviews: false）`)
        }
      })

      // 等待当前批次所有请求完成
      await Promise.all(promises)
      console.log(`✅ 批次处理完成，当前数据状态:`, {
        contentData: Object.keys(this.contentData),
        reviewData: Object.keys(this.reviewData),
        completedRequests: this.completedRequests,
        totalRequests: this.totalRequests
      })
    },

    // 加载单个章节内容
    async loadChapterContent(catalogueId) {
      const { data } = await getContent({ catalogueId })
      return data && data.length > 0 ? data[0].data : '<p>暂无内容</p>'
    },

    // 加载单个章节的审核意见
    async loadChapterReviews(catalogueId) {
      console.log(`🔍 正在加载章节 ${catalogueId} 的审核意见...`)

      // 使用与主页面一致的参数格式
      const response = await getDigitalBugList({
        digitalBookId: Number(this.bookId), // 转换为Number类型
        catalogueId: catalogueId
      })
      console.log(`📝 章节 ${catalogueId} 审核意见响应:`, response)

      const reviews = response.data || []
      console.log(`📊 章节 ${catalogueId} 审核意见数量: ${reviews.length}`)

      // 处理数据结构，与主页面保持一致
      if (reviews.length > 0) {
        reviews.forEach(item => {
          // 解析position数据
          if (item.postion) {
            try {
              const position = JSON.parse(item.postion)
              item.dataId = position.id
              item.text = position.text
            } catch (e) {
              console.warn('解析position数据失败:', e)
            }
          }
          item.commentList = item.commentList || []
        })
      }

      return reviews
    },

    // 组装完整内容
    async assembleCompleteContent() {
      this.loadingText = '正在组装完整文档...'

      let completeContent = ''

      // 添加文档头部信息
      completeContent += this.generateDocumentHeader()

      // 1. 先生成完整的目录索引（像真正的书籍）
      completeContent += this.generateTableOfContents()

      // 2. 再生成所有章节的具体内容
      completeContent += this.generateAllChapterContents()

      // 添加错误报告（如果有）
      if (this.errors.length > 0) {
        completeContent += this.generateErrorReport()
      }

      // 添加文档尾部
      completeContent += this.generateDocumentFooter()

      this.selectedContent = completeContent

      // 与打印功能完全一致的MathJax渲染逻辑
      await this.$nextTick()
      await window.MathJax.typesetPromise()
    },

    // 生成文档头部
    generateDocumentHeader() {
      const now = new Date()
      return `
        <div style="text-align: center; margin-bottom: 40px; padding: 20px; border-bottom: 2px solid #409EFF;">
          <h1 style="color: #409EFF; margin-bottom: 10px;">数字教材完整导出文档</h1>
          <p style="color: #666; font-size: 14px;">导出时间: ${now.toLocaleString()}</p>
          <p style="color: #666; font-size: 14px;">书籍ID: ${this.bookId}</p>
          <p style="color: #666; font-size: 14px;">总章节数: ${this.totalChapters}</p>
          <p style="color: #666; font-size: 14px;">包含审核意见: ${this.includeReviews ? '是' : '否'}</p>
        </div>
      `
    },

    // 生成完整的目录索引（与出版社审核页面样式一致）
    generateTableOfContents() {
      let tocContent = `
        <div style="margin: 20px 0; page-break-after: always;">
          <div style="color: #000; font-size: 16px; font-weight: bold; margin-bottom: 20px;">目录</div>
          <div style="font-family: inherit;">
      `

      tocContent += this.generateTOCStructure(this.catalogueData, 0)

      tocContent += `
          </div>
        </div>
      `

      return tocContent
    },

    // 递归生成目录结构（与出版社审核页面样式一致）
    generateTOCStructure(catalogues, level = 0) {
      let tocContent = ''

      catalogues.forEach((item) => {
        // 计算缩进，模拟Tree组件的层级缩进
        const indentPx = level * 18 // 每级缩进18px，与Element UI Tree一致

        // 使用与出版社审核页面一致的样式
        tocContent += `
          <div style="
            height: 40px;
            display: flex;
            align-items: center;
            padding-left: ${indentPx}px;
            padding-right: 8px;
            font-size: 14px;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
          ">
            <div style="
              flex: 1;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            ">
              ${item.title}
            </div>
          </div>
        `

        // 如果有子章节，递归处理（全部展开）
        if (item.childCatalogue && item.childCatalogue.length > 0) {
          tocContent += this.generateTOCStructure(item.childCatalogue, level + 1)
        }
      })

      return tocContent
    },

    // 生成所有章节的具体内容（与打印功能渲染方式一致）
    generateAllChapterContents() {
      let allContent = ''

      // 递归收集所有叶子节点（实际章节）
      const allChapters = this.collectAllChapterIds(this.catalogueData)

      // 使用与打印功能相同的拼接方式
      allChapters.forEach(chapter => {
        if (this.contentData[chapter.id]) {
          // 与打印功能完全一致的标题样式和内容拼接
          allContent += `<h3 style="width:100%;text-align: center;">${chapter.title}</h3>`
          allContent += this.contentData[chapter.id]

          // 添加审核意见（如果包含且有数据）
          if (this.includeReviews && this.reviewData[chapter.id] && this.reviewData[chapter.id].length > 0) {
            allContent += this.generateReviewSection(this.reviewData[chapter.id])
          }
        }
      })

      return allContent
    },

    // 生成单个章节的完整内容
    generateChapterContent(chapter, chapterNumber, titleLevel) {
      let chapterContent = ''

      // 章节标题
      chapterContent += `<h${titleLevel} style="color: #333; margin: 30px 0 20px 0; page-break-before: auto;">${chapterNumber} ${chapter.title}</h${titleLevel}>`

      // 章节内容
      const content = this.contentData[chapter.id] || '<p style="color: #999;">暂无内容</p>'
      chapterContent += `<div style="margin: 20px 0; line-height: 1.6;">${content}</div>`

      // 审核意见（如果包含且有数据）
      console.log(`🔍 章节 ${chapter.id} 审核意见检查:`, {
        includeReviews: this.includeReviews,
        hasReviewData: !!this.reviewData[chapter.id],
        reviewCount: this.reviewData[chapter.id] ? this.reviewData[chapter.id].length : 0,
        reviewData: this.reviewData[chapter.id]
      })

      if (this.includeReviews && this.reviewData[chapter.id] && this.reviewData[chapter.id].length > 0) {
        console.log(`✅ 为章节 ${chapter.id} 生成审核意见部分`)
        chapterContent += this.generateReviewSection(this.reviewData[chapter.id])
      } else {
        console.log(`❌ 章节 ${chapter.id} 不显示审核意见，原因:`, {
          includeReviews: this.includeReviews,
          hasData: !!this.reviewData[chapter.id],
          dataLength: this.reviewData[chapter.id] ? this.reviewData[chapter.id].length : 'undefined'
        })
      }

      // 章节分隔线
      chapterContent += '<hr style="margin: 40px 0; border: none; border-top: 1px solid #eee;">'

      return chapterContent
    },

    // 生成审核意见部分
    generateReviewSection(reviews) {
      console.log(`🎨 开始生成审核意见HTML，数据:`, reviews)

      let reviewContent = `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h4 style="color: #409EFF; margin-bottom: 15px;">审核意见</h4>
      `

      reviews.forEach((review, index) => {
        const statusColor = review.bugStatus === 'OPEN' ? '#E6A23C' : '#67C23A'
        const statusText = review.bugStatus === 'OPEN' ? '待解决' : '已解决'
        const reviewerName = review.user ? review.user.displayName || review.user.name || '未知用户' : '未知用户'

        reviewContent += `
          <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px; border-left: 4px solid ${statusColor}; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
              <span style="font-weight: bold; color: #333; font-size: 14px;">审核意见 ${index + 1}</span>
              <span style="background: ${statusColor}; color: white; padding: 3px 10px; border-radius: 12px; font-size: 12px; font-weight: 500;">${statusText}</span>
            </div>

            <!-- 审核意见内容 -->
            <div style="margin-bottom: 12px;">
              <div style="font-weight: 500; color: #333; margin-bottom: 6px; font-size: 13px;">💬 审核意见：</div>
              <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; color: #555; line-height: 1.5; font-size: 13px;">
                ${review.bugDescription || '无具体意见'}
              </div>
            </div>

            <!-- 原文内容引用 -->
            ${review.text ? `
            <div style="margin-bottom: 12px;">
              <div style="font-weight: 500; color: #333; margin-bottom: 6px; font-size: 13px;">📄 原文引用：</div>
              <div style="background: #fff3cd; border-left: 3px solid #ffc107; padding: 10px; border-radius: 4px; color: #856404; line-height: 1.5; font-size: 12px; max-height: 100px; overflow: hidden;">
                ${review.text.replace(/<[^>]*>/g, '').substring(0, 200)}${review.text.length > 200 ? '...' : ''}
              </div>
            </div>
            ` : ''}

            <!-- 审核人和时间信息 -->
            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 11px; color: #999; border-top: 1px solid #f0f0f0; padding-top: 8px;">
              <span>👤 审核人: ${reviewerName}</span>
              <span>🕒 ${review.createdAt || review.createTime || '未知时间'}</span>
            </div>
          </div>
        `
      })

      reviewContent += '</div>'

      console.log(`✅ 审核意见HTML生成完成，长度: ${reviewContent.length}`)
      console.log(`📄 生成的HTML内容:`, reviewContent)

      return reviewContent
    },

    close() {
      this.selectedContent = ''
      this.handleClose()
    },

    // 生成错误报告
    generateErrorReport() {
      if (this.errors.length === 0) return ''

      let errorContent = `
        <div style="background: #fef0f0; border: 1px solid #fbc4c4; padding: 20px; border-radius: 8px; margin: 30px 0;">
          <h3 style="color: #f56c6c; margin-bottom: 15px;">⚠️ 加载过程中的错误</h3>
          <ul style="margin: 0; padding-left: 20px;">
      `

      this.errors.forEach(error => {
        errorContent += `<li style="color: #f56c6c; margin: 5px 0;">${error}</li>`
      })

      errorContent += `
          </ul>
          <p style="color: #999; font-size: 12px; margin-top: 15px;">
            注意：部分内容可能因网络或权限问题未能正常加载，请检查网络连接或联系管理员。
          </p>
        </div>
      `

      return errorContent
    },

    // 生成文档尾部
    generateDocumentFooter() {
      return `
        <div style="text-align: center; margin-top: 50px; padding: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;">
          <p>本文档由数字教材系统自动生成</p>
          <p>生成时间: ${new Date().toLocaleString()}</p>
          <p>如有疑问，请联系系统管理员</p>
        </div>
      `
    },

    // 生成错误内容（当加载失败时显示）
    generateErrorContent(error) {
      return `
        <div style="text-align: center; padding: 50px; color: #f56c6c;">
          <h2>❌ 内容加载失败</h2>
          <p style="margin: 20px 0; font-size: 16px;">${error.message}</p>
          <p style="color: #999; font-size: 14px;">请检查网络连接或联系管理员</p>
        </div>
      `
    },



    // 导出文档方法
    async printContent() {
      if (!this.selectedContent) {
        this.$message.warning('暂无内容可导出')
        return
      }

      if (this.loading) {
        this.$message.warning('内容正在加载中，请稍候...')
        return
      }

      const richTextElement = this.$refs.richText
      let exportContent = richTextElement.innerHTML

      // 使用正则移除 font-family 样式（与数字教材打印保持一致）
      exportContent = exportContent.replace(/font-family:[^;"]*;?/gi, '')
      exportContent = exportContent.replace(/style="[^"]*font-family:[^;"]*;?[^"]*"/gi, '')

      this.loading = true
      this.isExporting = true

      try {
        this.$emit('export', {
          content: exportContent,
          format: 'word',
          totalChapters: this.totalChapters,
          includeReviews: this.includeReviews,
          errors: this.errors,
          bookId: this.bookId
        })

        // 简化的导出过程，减少等待时间
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 显示导出成功提示
        this.$message.success('导出成功！')

        // 延迟一下再关闭弹窗，让用户看到成功提示
        setTimeout(() => {
          this.handleClose()
        }, 1500)
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      } finally {
        this.loading = false
        this.isExporting = false
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    handleCancel() {
      this.handleClose()
    },
    async handleConfirm() {
      try {
        this.exporting = true

        this.$emit('export', {})

        await new Promise(resolve => setTimeout(resolve, 500))

        this.$message.success('导出成功！')
        this.handleClose()
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      } finally {
        this.exporting = false
      }
    },
    open() {
      this.$emit('update:visible', true)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .dialog-box {
  padding: 10px 10px 0 10px !important;
}

.export-container {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.rich-text-container {
  width: 100%;
  flex: 1;
  padding: 0 10px 70px 10px;
  position: relative;
  overflow: hidden;
}

.loading-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  max-width: 400px;

  .progress-info {
    margin-top: 20px;

    .el-progress {
      margin-bottom: 10px;
    }
  }
}

.tips {
  cursor: pointer;
  font-size: 10px;
  margin-left: 10px;
  margin-top: 0;
}

.emty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rich-text-content {
  height: 100%;
  overflow: auto;
  @include scrollBar;

  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
  color: #222f3e; // 默认文字颜色
  line-height: 1.4; // 默认行高

  ::v-deep * {
    max-width: 100%;
  }

  ::v-deep pre > code {
    display: block;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
  }

  ::v-deep p,
  ::v-deep div,
  ::v-deep span,
  ::v-deep h1,
  ::v-deep h2,
  ::v-deep h3,
  ::v-deep h4,
  ::v-deep h5,
  ::v-deep h6 {
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
    color: #222f3e !important;
  }
}

.export-footer {
  position: absolute;
  bottom: 0px;
  left: -10px;
  right: -10px;
  background: #ffffff;
  padding: 15px 20px;
  z-index: 10;
  border-radius: 0 0 15px 15px;
}

.export-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tips {
  cursor: pointer;
  font-size: 12px;
  color: #666;

  i {
    margin-left: 5px;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.export-btn {
  padding: 6px 16px;
  font-size: 13px;

  &.el-button--primary {
    background: #a8b2b9;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(168, 178, 185, 0.2);

    &:hover {
      background: #95a5a6;
      box-shadow: 0 4px 12px rgba(168, 178, 185, 0.3);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(168, 178, 185, 0.2);
    }

    &:disabled {
      background: #d5dbdb;
      box-shadow: none;
      transform: none;
      opacity: 0.6;
    }
  }

}

.cancel-btn.el-button {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  color: #6c757d !important;
  border-radius: 8px !important;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

  &:hover,
  &:hover:focus,
  &.hover {
    background: #e9ecef !important;
    border: 1px solid #dee2e6 !important;
    border-color: #dee2e6 !important;
    color: #495057 !important;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15) !important;
  }

  &:active,
  &:active:focus,
  &.active {
    background: #e9ecef !important;
    border: 1px solid #dee2e6 !important;
    border-color: #dee2e6 !important;
    color: #495057 !important;
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  &:focus {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-color: #e9ecef !important;
    color: #6c757d !important;
    outline: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }
}

.export-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.export-loading {
  text-align: center;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
