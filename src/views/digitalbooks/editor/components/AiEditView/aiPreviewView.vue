<template>
  <div class="preview-main" v-if="previewShow" v-loading="loading">
    <div class="preview-close" @click="handleClose">
      <i class="el-icon-close"></i>
    </div>
    <div class="preview-title">
      预览
    </div>
    <div class="preview-content" ref="scrollContainer">
      <div ref='renderDivRef' v-if="treeData.length === 0" style='width: 100%;height: 100%' v-html="renderedHtml"></div>
      <el-tree
        v-else
        ref="treeSelect"
        :data="treeData"
        :props="treeProps"
        node-key="tempId"
        default-expand-all
        highlight-current
        :expand-on-click-node="false"
        :draggable="true"
      >
        <div slot-scope="{ node, data }" class="tree-body">
          <div class="chapter-name" :class="{'chapter-name-1' : data.depth === 0, 'chapter-name-3' : data.depth === 2}">
            <div :title="data.title" class="w article-singer-container">
              {{ data.title }}
            </div>
          </div>
          <div class="chapter-option">
            <el-tooltip class="item" effect="dark" content="新增" placement="top-start">
              <el-button
                type="text"
                size="mini"
                @click.stop="() => treeAppend(node, data)"
              >
                <svg-icon
                  class="svgIcon"
                  icon-class="add"
                  class-name="add"
                />
              </el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
              <el-button
                type="text"
                size="mini"
                @click.stop="() => treeEdit(node, data)"
              >
                <svg-icon
                  class="svgIcon"
                  icon-class="edit"
                  class-name="edit"
                />
              </el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
              <el-button
                type="text"
                size="mini"
                @click.stop="() => treeRemove(node, data)"
              >
                <svg-icon
                  class="svgIcon"
                  icon-class="delete"
                  class-name="delete"
                />
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </el-tree>
    </div>
    <div class="preview-bottom">
      <div class="btn-view" @click="handleRegenerate">重新生成</div>
      <div class="btn-view btn-primary" @click="handleConfirm">确定使用</div>
    </div>
    <NormalDialog
      v-if="dialogShow"
      title="提示"
      width="400px"
      :dialog-visible="dialogShow"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      :hasFooter="false"
      @closeDialog="dialogShow = false">
      <div class="dialog-body" v-loading="dialogLoading">
        <img :src='tipImg' alt='' class='dialog-img' />
        <div class="dialog-title">
          {{type === 'menu' ? '是否确定使用该目录' : '是否确定使用该内容'}}
        </div>
        <div class="dialog-btn">
          <div class="btn" @click="dialogShow = false">取消</div>
          <div class="btn btn-primary" @click="handleSecondConfirm">确定</div>
        </div>
      </div>
    </NormalDialog>
    <editChapter
      v-if="editChapterShow"
      :show="editChapterShow"
      :append-to-body="true"
      :node-info="currChapter"
      :is-static="true"
      @close="editChapterShow = false"
      @emitSuccess="editDone"
    />
  </div>
</template>

<script>
import editChapter from '../editChapter.vue'
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import { batchEditBookCatalogue } from '@/api/digital-api.js'
import { Marked } from 'marked'
import { markedHighlight } from "marked-highlight";
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';
import "highlight.js/styles/paraiso-light.css"
export default {
  name: 'AiPreviewView',
  components: {
    NormalDialog,
    editChapter
  },
  props: {
    type: {
      type: String,
      default: 'menu' // 默认是目录生成
    },
    bookTitle: {
      type: String,
      default: ''
    },
    chapterTitle: {
      type: String,
      default: ''
    },
    configTitle: {
      type: String,
      default: ''
    },
    treeStr: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tipImg: require('@/assets/digitalbooks/edit/tip.png'),
      loading: false,
      previewShow:false,
      originalInfo: null,
      bookId: this.$route.query.id || null,
      renderedHtml: '',
      originalHtml:'',
      treeData:[],
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      isWorking: false,
      dialogShow: false,
      dialogLoading: false,
      editChapterShow: false,
      currChapter: null,
      result: false
    };
  },
  methods: {
    handleClose() {
      this.previewShow = false
      this.originalInfo = null
      this.renderedHtml = ''
      this.treeData = []
      this.result = false
    },
    handleRegenerate() {
      if (this.isWorking) return
      this.$emit('handleRegenerate');
      this.renderedHtml = ''
      this.treeData = []
      this.getPreviewInfo()
    },
    handleConfirm() {
      if (this.isWorking) return
      this.dialogShow = true

    },
    async handleSecondConfirm() {
      if (this.type === 'menu') {
        this.dialogLoading = true
        try {
          const params = {
            bookId: this.bookId,
            type: 'CHAPTER',
            childCatalogue: this.treeData,
          }
          await batchEditBookCatalogue(params)
          this.dialogShow = false
          this.$emit('confirm', this.type);
          this.handleClose()
        } catch (e) {
          console.log(e)
        } finally {
          this.dialogLoading = false
        }
      } else {
        const el = this.$refs.renderDivRef
        const newHtml = this.processMjxHtml(el.innerHTML, this.renderedHtml)
        const processHtml = this.addLanguageMarkupToPre(this.renderedHtml)
        this.$emit('confirm', this.type, processHtml);
      }
    },
    // 为代码块增加背景色
    addLanguageMarkupToPre(htmlString) {
      // 创建一个临时DOM元素来解析HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlString
      // 获取所有pre标签
      const preTags = tempDiv.getElementsByTagName('pre')
      // 为每个pre标签添加language-markup类
      for (let i = 0; i < preTags.length; i++) {
        const pre = preTags[i]
        const codeTag = pre.querySelector('code')
        if (codeTag && codeTag.className) {
          // 将code标签的类名拆分为数组
          const codeClasses = codeTag.className.split(/\s+/);
          // 为pre标签添加这些类名
          codeClasses.forEach(className => {
            if (className && !pre.classList.contains(className)) {
              pre.classList.add(className)
            }
          })
        }
      }
      // 返回处理后的HTML字符串
      return tempDiv.innerHTML
    },
    processMjxHtml(htmlString, originalHtml) {
      // 检查输入是否为有效的字符串
      if (typeof htmlString !== 'string' || htmlString.trim() === '') {
        return htmlString
      }
      const mjxRegex = /(<mjx-container[^>]*>[\s\S]*?<\/mjx-container>)/gi
      // 替换匹配到的内容，添加两层span包裹
      return htmlString.replace(mjxRegex, (match) => {
        // return `<span class="match-tex" contenteditable="false" data-latex="\\({a}^{2}+{b}^{2}={c}^{2}\\)"><span class="dummy" hidden="hidden">dummy</span></span>`;
        // return `<div style="width: 100px;height: 100px;background-color: yellow"></div>`
        return `<span class="match-tex" contenteditable="false"><span class="math-tex-original">${match}</span><span class="dummy" hidden="hidden">dummy</span></span>`;
      })
    },
    open(dataInfo) {
      this.previewShow = true
      this.originalInfo = dataInfo
      this.getPreviewInfo()
    },
    async getPreviewInfo() {
      try {
        this.loading = true
        this.isWorking = true
        const url = `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat`
        const systemList = this.type === 'menu' ? [
          {
            role: 'system',
            content: `现要编写一本书，书名为《${this.bookTitle}》请为这本书生成目录大纲，除了生成的目录，不要返回额外的文字（章用一个井号标识，章下面的节用两个井号标识，第三级用三个井号标识），目录分章和节，最大不超过3级，尽量精简务实，条理清楚。${this.desc !==''? `本书的简介：${this.desc}。` : ''}`
          }
        ] : [
          {
            role: 'system',
            content: `本书名为《${this.bookTitle}》，请为${this.configTitle}编写内容，不要返回额外的文字；尽量精简务实，条理清楚，不要冗余啰嗦；要结合本书的目录大纲，参考上下文来生成内容，避免断层和重复；本书目录如下：${this.treeStr}；甚至可以指定格式`
          }
        ]
        let list = this.originalInfo.fileList.reduce((pre, cur) => {
            pre.push({
              role: 'system',
              content: cur.fileData
            })
            return pre
          }, systemList)
        list.push({
          role: 'user',
          content: this.originalInfo.tipStr
        })
        let body = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(list)
        }
        const response = await fetch(url, body)
        if (!response.ok) throw new Error('Network response was not ok')
        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        this.result = true
        let res = ''
        while (this.result) {
          const { done, value } = await reader.read()
          if (done) {
            this.result = false
            if (this.type === 'menu') {
              this.treeData = this.textToTree(this.originalHtml)
            } else {
              this.treeData = []
            }
            this.scrollToTop()
            this.$nextTick(() => {
              window.MathJax.typesetPromise()
            })
            break
          }
          const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
          const arr = chunkText.split('-down-')
          arr.forEach(item => {
            if (this.isJSON(item)) {
              if (JSON.parse(item).data.result) {
                res += JSON.parse(item).data.result
              }
            }
          })
          this.originalHtml = res
          this.renderedHtml = this.markMessage(res)
          this.loading = false
          if (!this.renderedHtml || this.renderedHtml === ''){
            this.$message.warning('生成失败，请重新生成')
          }
          this.scrollToBottom()
        }
      } catch (e) {
        this.$message.warning('生成失败，请重新生成')
        this.loading = false
      } finally {
        this.isWorking = false
      }
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    markMessage(message) {
      message=message.replaceAll('\\n','\n')
      // console.log('调用前'+message)
      const marked = new Marked(
        markedHighlight({
          pedantic: false,
          gfm: true, // 开启gfm
          breaks: true,
          smartLists: true,
          xhtml: true,
          async: false, // 如果是异步的，可以设置为 true
          langPrefix: 'hljs language-', // 可选：用于给代码块添加前缀
          emptyLangClass: 'no-lang', // 没有指定语言的代码块会添加这个类名
          highlight: (code) => {
            return hljs.highlightAuto(code).value
          }
        })
      );
      let  markedMessage = marked.parse(message)
      // console.log('调用了'+markedMessage)
      return markedMessage
    },
    textToTree(text) {
      const lines = text.split('\n')
        .map(line => line.trim())
        .filter(line => line)

      const tree = []
      const stack = []// 用于保存各级节点的引用，用于构建父子关系

      lines.forEach(line => {
        // 匹配井号数量和内容
        const match = line.match(/^(#{1,3})\s+(.*)$/);
        if (!match) return; // 跳过不符合格式的行
        const [, hash, label] = match
        const level = hash.length; // 1: 一级, 2: 二级, 3: 三级
        const id = this.generateId(label, level, stack); // 生成唯一ID
        // 创建节点
        const node = { tempId:id, childCatalogue: [], title: label }
        // 根据层级处理父子关系
        if (level === 1) {
          // 一级节点直接加入树形
          tree.push(node);
          // 更新栈，只保留当前一级节点
          stack.length = 0
          node.depth = 0
          stack.push(node)
        } else if (level === 2) {
          // 二级节点，其父节点是最后一个一级节点
          if (stack.length >= 1) {
            stack[0].childCatalogue.push(node)
            // 更新栈，保留一级和当前二级节点
            stack.length = 1
            node.depth = 1
            stack.push(node)
          }
        } else if (level === 3) {
          // 三级节点，其父节点是最后一个二级节点
          if (stack.length >= 2) {
            node.depth = 2
            stack[1].childCatalogue.push(node)
            // 栈保持一级和二级节点，三级节点不进栈
          }
        }
      })
      return tree
    },
    generateId(label, level, stack) {
      let timestamp = new Date().getTime()
      return `node-${level}-${timestamp}-${label}`
    },
    // 滚动到最底部的方法
    scrollToBottom() {
      // 使用 $nextTick 确保 DOM 已更新
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer
        if (container) {
          // 设置滚动条位置到最底部
          container.scrollTop = container.scrollHeight
        }
      })
    },
    // 滚动到最顶部的方法
    scrollToTop() {
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer
        if (container) {
          // 设置滚动条位置到最顶部
          container.scrollTop = 0
        }
      })
    },
    treeAppend (node, data) {
      this.currChapter = { bookId: this.bookId, parentId: data.tempId, data: null }
      this.editChapterShow = true
    },
    treeEdit (node, data) {
      this.currChapter = { bookId: this.bookId, parentId: data.parentId, data }
      this.editChapterShow = true
    },
    treeRemove (node, data) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.deleteNodeById(this.treeData, data.tempId)
      })
    },
    editDone (chapterName, nodeInfo) {
      this.editChapterShow = false
      if (nodeInfo.data) { //编辑
        const currentNode = this.findNodeById(this.treeData, nodeInfo.data.tempId)
        if (currentNode) {
          currentNode.title = chapterName
        }
      } else { //新增
        const currentNode = this.findNodeById(this.treeData, nodeInfo.parentId)
        if (currentNode) {
          if (!currentNode.childCatalogue) currentNode.childCatalogue = []
          currentNode.childCatalogue.push({
            tempId: `node-${currentNode.depth + 2}-${new Date().getTime()}-${chapterName}`,
            title: chapterName,
            childCatalogue: [],
            depth: currentNode.depth + 1
          })
        }
      }
    },
    findNodeById(tree, tempId) {
      for (const node of tree) {
        if (node.tempId === tempId) {
          return node
        }
        if (node.childCatalogue && node.childCatalogue.length > 0) {
          const foundNode = this.findNodeById(node.childCatalogue, tempId)
          if (foundNode) {
            return foundNode
          }
        }
      }
      return null
    },
    deleteNodeById(tree, tempId) {
      // 遍历当前层级的节点
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].tempId === tempId) {
          tree.splice(i, 1);
        }
        if (tree[i].childCatalogue && tree[i].childCatalogue.length > 0) {
          this.deleteNodeById(tree[i].childCatalogue, tempId);
        }
      }
    }
  },
}
</script>

<style scoped lang='scss'>
.preview-main{
  position: absolute;
  width: 600px;
  max-height: 550px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 30px;
  z-index: 100;
  .preview-close{
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    &:hover {
      color: #333;
    }
  }
  .preview-title{
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
  }
  .preview-content{
    height: 350px;
    overflow-y: auto;
    border: 1px solid #d9d9d9;
    border-radius: 10px;
    padding: 0 40px 40px 40px;
  }
  .preview-bottom{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    .btn-view{
      width: 160px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      margin-left: 10px;
      color: rgba(47, 128, 237, 1);
      border: 1px solid rgba(47, 128, 237, 1);
      background-color: white;
      font-size: 14px;
    }
    .btn-primary{
      background-color: rgba(47, 128, 237, 1);
      color: white;
    }
  }
}
.tree-body {
  width: calc(100% - 30px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .chapter-name {
    flex: 1;
    overflow: hidden;
    color: black;
    @include scrollBar;
  }
  .chapter-name-1 {
    font-weight: 800 !important;
    font-size: 16px !important;
  }
  .chapter-name-3 {
    color: #5c5a5a;
  }
  .chapter-option {
    flex-shrink: 0;
    .red_idot{
      display: inline-block;
      margin-right: 10px;
      width: 15px;
      height: 15px;
      font-size: 12px;
      font-weight: 700;
      border-radius: 10px;
      background: red;
      color: #fff;
      text-align: center;
      line-height: 15px;
    }
  }
}
.dialog-body{
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .dialog-img{
    width: 100px;
    height: 100px;
    object-fit: contain;
  }
  .dialog-title{
    margin-top: 10px;
    font-size: 16px;
    font-weight: 600;
  }
  .dialog-btn{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    margin-bottom: 40px;
    .btn{
      width: 120px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      margin-left: 10px;
      color: rgba(47, 128, 237, 1);
      border: 1px solid rgba(47, 128, 237, 1);
      background-color: white;
      font-size: 14px;
    }
    .btn-primary{
      background-color: rgba(47, 128, 237, 1);
      color: white;
    }
  }
}
</style>

<style lang='scss'>
.preview-content{
  h1 {
    font-size: 16px !important;
  }
  h2 {
    font-size: 14px !important;
  }
  h3 {
    font-size: 14px !important;
  }
  th {
    border: 1px solid #bbb;
  }
  td {
    border: 1px solid #bbb;
  }
  table {
    border-collapse: collapse !important;
  }
}
</style>
