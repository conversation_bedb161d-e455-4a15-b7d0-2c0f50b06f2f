<template>
  <transition name="fade">
    <div
      v-show="showMenu"
      :style="{ top: position.top + 'px', left: position.left + 'px' }"
      class="dig-noteContent"
    >
      <div v-for="(item, index) in itemList" :key="index" class="item" @mousedown="clear" @click.stop="navFun(index)">
        <img :src="item.src" alt="" />
        <p>{{ item.title }}</p>
      </div>
      <!-- <div class="color_select">
        <div v-for="item in color " :key="item" :style="{backgroundColor:item}" class="color"></div>
      </div> -->
    </div>
  </transition>
</template>

<script>
export default {
  name: 'NoteMenu',
  props: {
    position: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      showMenu: false,
      color: ['#FBEC7F', '#B6D8FB', '#CAEC82', '#F4B4CA', '#D2B3FA'],
      itemList: [{
        title: '高亮',
        src: require('@/assets/digitalbooks/noteNav/heigh_light.svg')
      },
      // {
      //   title: '选色',
      //   src: require('@/assets/digitalbooks/noteNav/setColor.svg')
      // },
      {
        title: '笔记',
        src: require('@/assets/digitalbooks/noteNav/note.svg')
      }, {
        title: '复制',
        src: require('@/assets/digitalbooks/noteNav/copy.svg')
      }
      // {
      //   title: '百科',
      //   src: require('@/assets/digitalbooks/noteNav/word.svg')
      // }
      ]
    }
  },
  methods: {
    show () {
      this.showMenu = true
    },
    hide () {
      this.showMenu = false
    },
    clear (e) {
      e.preventDefault()
    },
    navFun (index) {
      switch (index) {
        case 0 :
          this.$emit('heighLight'); break
        case 1 :
          this.$emit('addNote'); break
        case 2 :
          this.$emit('copyText'); break
        // case 3 :
        //   this.$emit('baidu'); break
      }
    }
  }

}
</script>

<style scoped lang='scss'>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.25s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}

.dig-noteContent {
  display: flex;
  padding: 12px 0px;
  position: fixed;
  z-index: 1000;
  flex-direction: column;
  align-items: flex-end;
  // gap: 10px;
  border-radius: 10px;
  border: 1px solid #f2f2f2;
  background: #fff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  width: 91px;
  height: 150px;
  .item {
    width: 100%;
    height: 42px;
    text-align: center;
    display: flex;
    justify-content:center;
    gap:5px;
    cursor: pointer;
    img{
      width: 22px;
    }
    p{
      white-space:nowrap;
      margin-top: 13px;
      font-size: 14px;
    }
  }
  .item:hover{
    background-color: #e8f4ff;
    color: #46a6ff;
}
.color_select{
  width:36px;
  height:136px;
  border-radius: 3px;
  background: #fff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: absolute;
  right:-60px;
  padding:10px;
  .color{
    width: 15px;
    height:15px;
    border-radius: 7.5px;
    margin-top: 7px;
  }
}
}
</style>
