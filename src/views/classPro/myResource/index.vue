<template>
  <div class="index-box">
    <div class="card-box h">
      <div class="card-title-box">
        <div class="t-title">数字资源</div>
      </div>

      <div class="w body-h">
        <Empty v-if="resourceList.length === 0" />
        <div class="resource-list">
          <div v-for="item in resourceList" :key="item.id" class="resource-item" :class="{ 'resource-item-active': selectedResourceId === item.id }" @mouseenter="handleMouseEnter(item.id)" @mouseleave="handleMouseLeave" @click="handleClick(item.aicourse.id)">
            <div class="resource-item-left">
              <img v-if="selectedResourceId !== item.id" src="@/assets/images/resource/file.png" alt="pdf" />
              <img v-else src="@/assets/images/resource/file_select.png" alt="pdf" />
              <div class="resource-item-left-name">{{ item.aicourse.title }}</div>
            </div>
            <div class="resource-item-left-time">{{ item.aicourse.createdAt }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Empty from '@/components/classPro/Empty/index.vue'
import { getStudentCourseList } from '@/api/course-api'
export default {
  components: {
    Empty
  },
  data () {
    return {
      resourceList: [],
      selectedResourceId: 0
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    handleMouseEnter (id) {
      this.selectedResourceId = id
    },
    handleMouseLeave () {
      this.selectedResourceId = 0
    },
    handleClick (id) {
      this.$router.push({
        path: '/classpro/myResource/detail/' + id
      })
    },
    async getList () {
      const { data } = await getStudentCourseList({
        resourceType: 'DIGITAL_RESOURCE',
        studentCourseListType: 'ASSISTANT'
      })
      this.resourceList = data
    }
  }
}
</script>

  <style lang="scss" scoped>
  .index-box {
    width: 100%;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    @include scrollBar;
    background: #FFF;
    border-radius: 10px;

    .body-h {
      height: calc(100% - 50px);
      /*增加滚动条*/
      overflow-y:auto;
      overflow-x: hidden;
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 5px;
        height: 1px;
      }
      .resource-list {
        .resource-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;
          padding: 10px;
          border-radius: 5px;
          &:hover {
            background: linear-gradient(270deg, #00D2FF 0%, #3A7BD5 111.54%);
            color: #FFF;
          }
          .resource-item-left {
            display: flex;
            align-items: center;
            img {
              width: 20px;
              height: 20px;
            }
            .resource-item-left-name{
              margin-left: 10px;
            }
          }
          }
        }
      }

    .card-box {
      padding: 5px 15px;
      margin-bottom: 10px;
      box-sizing: border-box;

      &:last-child {
        margin-bottom: 0;
      }

      .card-title-box {
        margin: 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #E5E5E5;

        .t-title {
          color: #000;
          font-size: var(--font-size-L);
          font-weight: 500;
        }
      }
    }
  }
  </style>
